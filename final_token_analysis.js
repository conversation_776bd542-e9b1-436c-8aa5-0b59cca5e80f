/**
 * 最终token分析 - 寻找复现方法
 */

console.log('=== 最终token分析 ===\n');

// 关键数据
const ACTUAL_TIMESTAMP = 1754383264519;
const ACTUAL_TOKEN = '7jdcpm';
const XOR_ARRAY = [2212, 53, 2852, 3445, 3327];

console.log(`目标时间戳: ${ACTUAL_TIMESTAMP}`);
console.log(`目标token: ${ACTUAL_TOKEN}`);
console.log(`XOR数组: [${XOR_ARRAY.join(', ')}]\n`);

// 分析目标token的字符
function analyzeTargetToken() {
    console.log('1. 分析目标token字符:');
    const chars = 'abcdefghijklmnopqrstuvwxyz0123456789';
    const token = ACTUAL_TOKEN;
    
    for (let i = 0; i < token.length; i++) {
        const char = token[i];
        const index = chars.indexOf(char);
        const ascii = char.charCodeAt(0);
        console.log(`  位置${i}: '${char}' -> 索引${index}, ASCII${ascii}`);
    }
    
    const indices = token.split('').map(c => chars.indexOf(c));
    console.log(`  索引序列: [${indices.join(', ')}]\n`);
    
    return indices;
}

// 分析时间戳
function analyzeTimestamp() {
    console.log('2. 分析时间戳:');
    const timeStr = ACTUAL_TIMESTAMP.toString();
    console.log(`  时间戳字符串: ${timeStr}`);
    console.log(`  长度: ${timeStr.length}`);
    
    for (let i = 0; i < 6; i++) {
        const char = timeStr[i % timeStr.length];
        const value = parseInt(char);
        console.log(`  位置${i}: 字符'${char}' -> 值${value}`);
    }
    console.log('');
    
    return timeStr;
}

// 尝试逆向工程
function reverseEngineer() {
    console.log('3. 尝试逆向工程:');
    
    const chars = 'abcdefghijklmnopqrstuvwxyz0123456789';
    const targetIndices = [33, 9, 3, 2, 15, 12]; // 7jdcpm的索引
    const timeStr = ACTUAL_TIMESTAMP.toString();
    
    console.log('尝试找到生成规律...');
    
    // 尝试不同的组合方式
    for (let method = 1; method <= 10; method++) {
        console.log(`\n方法${method}:`);
        let token = '';
        let success = true;
        
        for (let i = 0; i < 6; i++) {
            let calculatedIndex = -1;
            
            switch (method) {
                case 1: // 简单时间戳映射
                    calculatedIndex = parseInt(timeStr[i % timeStr.length]) * (i + 1) % chars.length;
                    break;
                    
                case 2: // XOR与时间戳
                    calculatedIndex = (XOR_ARRAY[i % XOR_ARRAY.length] ^ parseInt(timeStr[i % timeStr.length])) % chars.length;
                    break;
                    
                case 3: // 时间戳哈希
                    const hash = ACTUAL_TIMESTAMP % 1000000;
                    calculatedIndex = (hash + i * 7) % chars.length;
                    break;
                    
                case 4: // 复杂XOR
                    const timeValue = parseInt(timeStr[i % timeStr.length]) || 0;
                    const xorValue = XOR_ARRAY[i % XOR_ARRAY.length];
                    calculatedIndex = (xorValue ^ (timeValue * 13) ^ (i * 7)) % chars.length;
                    break;
                    
                case 5: // 基于ASCII
                    const asciiSum = timeStr.split('').reduce((sum, c) => sum + c.charCodeAt(0), 0);
                    calculatedIndex = (asciiSum + i * XOR_ARRAY[i % XOR_ARRAY.length]) % chars.length;
                    break;
                    
                case 6: // 时间戳分段
                    const segment = parseInt(timeStr.substring(i * 2, i * 2 + 2)) || 0;
                    calculatedIndex = (segment ^ XOR_ARRAY[i % XOR_ARRAY.length]) % chars.length;
                    break;
                    
                case 7: // 简单加法
                    calculatedIndex = (parseInt(timeStr[i % timeStr.length]) + XOR_ARRAY[i % XOR_ARRAY.length] + i) % chars.length;
                    break;
                    
                case 8: // 乘法组合
                    calculatedIndex = (parseInt(timeStr[i % timeStr.length]) * XOR_ARRAY[i % XOR_ARRAY.length] * (i + 1)) % chars.length;
                    break;
                    
                case 9: // 位移操作
                    calculatedIndex = ((XOR_ARRAY[i % XOR_ARRAY.length] >> (i + 1)) ^ parseInt(timeStr[i % timeStr.length])) % chars.length;
                    break;
                    
                case 10: // 复合运算
                    const base = parseInt(timeStr.substring(i, i + 3)) || parseInt(timeStr[i % timeStr.length]);
                    calculatedIndex = (base + XOR_ARRAY[i % XOR_ARRAY.length] * (i + 1)) % chars.length;
                    break;
            }
            
            const char = chars[calculatedIndex];
            const expectedChar = ACTUAL_TOKEN[i];
            
            console.log(`  位置${i}: 计算索引${calculatedIndex} -> '${char}' (期望'${expectedChar}') ${char === expectedChar ? '✅' : '❌'}`);
            
            if (char !== expectedChar) {
                success = false;
            }
            
            token += char;
        }
        
        console.log(`  结果: ${token} ${success ? '✅ 完全匹配!' : '❌'}`);
        
        if (success) {
            console.log(`\n🎉 找到匹配的方法${method}!`);
            return method;
        }
    }
    
    return null;
}

// 基于混淆代码的分析
function analyzeObfuscatedCode() {
    console.log('\n4. 基于混淆代码的分析:');
    
    // 从混淆代码中我们知道有eval调用
    // eval(__qxBY2hC(32) + __qxBY2hC(...) + ...)
    // __qxBY2hC = String.fromCharCode
    
    console.log('混淆代码结构:');
    console.log('- __qxBY2hC = String.fromCharCode');
    console.log('- _x_sEd = [2212, 53, 2852, 3445, 3327]');
    console.log('- 5个XOR函数: _$3vge, _$89tQ, _$2ez, _$zcp, _$mRym');
    console.log('- 最后调用eval(构建的字符串)');
    
    // 尝试手动计算一些关键值
    const _x_sEd = [2212, 53, 2852, 3445, 3327];
    const __qxBY2hC = String.fromCharCode;
    
    const _$3vge = (x) => x ^ _x_sEd[0];
    const _$89tQ = (x) => x ^ _x_sEd[1];
    const _$2ez = (x) => x ^ _x_sEd[2];
    const _$zcp = (x) => x ^ _x_sEd[3];
    const _$mRym = (x) => x ^ _x_sEd[4];
    
    console.log('\n计算一些示例值:');
    console.log(`__qxBY2hC(55, 106, 100, 99, 112, 109) = "${__qxBY2hC(55, 106, 100, 99, 112, 109)}"`);
    console.log(`_$3vge(2250) = ${_$3vge(2250)}`);
    console.log(`_$2ez(2835) = ${_$2ez(2835)}`);
    
    // 尝试构建可能的token字符
    const possibleChars = [];
    for (let i = 0; i < 256; i++) {
        const char = String.fromCharCode(i);
        if (/[a-z0-9]/.test(char)) {
            possibleChars.push({char, ascii: i});
        }
    }
    
    console.log(`\n可能的token字符: ${possibleChars.map(c => c.char).join('')}`);
    
    // 检查目标token的ASCII值
    console.log('\n目标token的ASCII值:');
    for (let i = 0; i < ACTUAL_TOKEN.length; i++) {
        const char = ACTUAL_TOKEN[i];
        const ascii = char.charCodeAt(0);
        console.log(`'${char}' -> ASCII ${ascii}`);
        
        // 检查是否能通过XOR函数得到
        for (let j = 0; j < _x_sEd.length; j++) {
            const xorResult = ascii ^ _x_sEd[j];
            console.log(`  ${ascii} ^ ${_x_sEd[j]} = ${xorResult}`);
        }
    }
}

// 主函数
function main() {
    const targetIndices = analyzeTargetToken();
    const timeStr = analyzeTimestamp();
    const matchingMethod = reverseEngineer();
    analyzeObfuscatedCode();
    
    console.log('\n=== 总结 ===');
    if (matchingMethod) {
        console.log(`✅ 找到了匹配的方法${matchingMethod}!`);
        console.log('可以用这个方法来复现token生成。');
    } else {
        console.log('❌ 没有找到简单的复现方法。');
        console.log('token生成可能需要执行完整的混淆代码。');
    }
    
    console.log('\n建议:');
    console.log('1. 如果找到匹配方法，实现到主脚本中');
    console.log('2. 如果没找到，考虑使用固定token或其他策略');
    console.log('3. 继续分析混淆代码的eval部分');
}

// 运行分析
main();
