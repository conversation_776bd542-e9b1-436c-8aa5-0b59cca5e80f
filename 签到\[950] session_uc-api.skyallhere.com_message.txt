GET /miniprogram/api/v1/get-user HTTP/1.1
Host: uc-api.skyallhere.com
Connection: keep-alive
App-Path: /pages/user/user
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJvcGVuaWQiOiJvTVNTZzRtR2dnc0hNUWp0TmdBVXNYLVJ1aUpzIiwidW5pb25pZCI6Im9BdWVHamtzTHdieFRPY2tGejAzS2I5NWExTzgiLCJ1c2VyaWQiOjE5NDYwMzc5LCJ1c2VyX2NvZGUiOiIxYWI4ODgxNGNjNDhmNzdmIiwidXNlcl9waG9uZSI6IjE4ODA4MzMzMzk2Iiwibmlja19uYW1lIjoiIiwiZXhwIjoxNzU0Mzc2NTMxfQ.rOAXDkwcQHswBIS6JH1UdvG4cjYwj7926DvSvfd1_dM
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF WindowsWechat(0x63090c37)XWEB/14315
Content-Type: application/json
App-System: Windows 10 x64
xweb_xhr: 1
App-Model: microsoft
App-Sdkversion: 3.9.0
App-Version: 3.9.12
Accept: */*
Sec-Fetch-Site: cross-site
Sec-Fetch-Mode: cors
Sec-Fetch-Dest: empty
Referer: https://servicewechat.com/wxff438d3c60c63fb6/342/page-frame.html
Accept-Encoding: gzip, deflate, br
Accept-Language: zh-CN,zh;q=0.9



HTTP/1.1 200 OK
Date: Tue, 05 Aug 2025 06:18:54 GMT
Content-Type: application/json; charset=utf-8
Content-Length: 1081
Connection: keep-alive
X-Request-Id: c035918f6946ce5b93911acb6f008e32
Access-Control-Allow-Origin: *
Access-Control-Allow-Credentials: true

{"code":0,"data":{"baseInfo":{"address":"","birthday":"1985-08-02 00:00:00","city":"","county":"","growthValue":320,"headImg":"https://obs-yonghuyunying.obs.cn-south-1.myhuaweicloud.com/member-center/static/avatar-default.png","inviteTag":"1ab88814cc48f77f","latitude":"","longitude":"","nickName":"啵啵","openid":"oMSSg4mGggsHMQjtNgAUsX-RuiJs","phone":"18808333396","province":"","todayGrowth":0,"todayScore":0,"unionid":"oAueGjksLwbxTOckFz03Kb95a1O8","userGender":1,"userGrade":1,"userScore":305,"vipLevel":1},"extra":{"prizePage":"https://uc-api.skyallhere.com/miniprogram/tp/duiba-nologin?dbredirect=%2F%2F74367.activity-13.m.duiba.com.cn%2Fcrecord%2Frecord%3Fdbnewopen"},"memberGrade":[{"levelGrade":1,"levelName":"基础","max":999,"min":1},{"levelGrade":2,"levelName":"白银","max":11999,"min":1000},{"levelGrade":3,"levelName":"黄金","max":24999,"min":12000},{"levelGrade":4,"levelName":"钻石","max":75999,"min":25000},{"levelGrade":5,"levelName":"黑金","max":-1,"min":76000}],"product":[],"taskProgress":{"isCanUploadInvoice":true,"isImproved":false}},"msg":"ok"}