# -*- coding: utf-8 -*-
import os
from urllib.parse import quote

# 原始URL
original_url = "https://mcs-mimp-web.sf-express.com/up-member/newHome?mobile=181****5357&userId=73FE37E7FC644FF5BD7709C54B895007&path=/up-member/newHome&supportShare=NO&maskType=autoReceive&from=surprise_benefitwxauto&equityKey=surprise_benefit&citycode=833&cityname=乐山"

# 方法1：手动替换&符号
encoded_url = original_url.replace('&', '%26')

print("原始URL:")
print(original_url)
print("\n编码后URL:")
print(encoded_url)

# 设置环境变量
os.environ['sfsyUrl'] = encoded_url

print("\n开始测试...")

# 运行脚本
if __name__ == "__main__":
    exec(open('顺丰1.1.py', encoding='utf-8').read())
