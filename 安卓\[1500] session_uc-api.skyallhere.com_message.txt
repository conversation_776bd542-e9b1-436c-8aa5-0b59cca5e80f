GET /miniprogram/api/v1/banners/bnminemid?position=bnminemid HTTP/1.1
Host: uc-api.skyallhere.com
Connection: keep-alive
authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJvcGVuaWQiOiJvTVNTZzRnbXBwOWp5ajJaS2F4OGc3TmhsUEJJIiwidW5pb25pZCI6Im9BdWVHamp0T1hUQmRuS2lhUDk2U0VUcUZiNTQiLCJ1c2VyaWQiOjE5NDYwMzk3LCJ1c2VyX2NvZGUiOiI0MmQ0MDY2MGRkOTIyOGMwIiwidXNlcl9waG9uZSI6IjE4MTExNDU1NjI3Iiwibmlja19uYW1lIjoiIiwiZXhwIjoxNzU0Mzg0OTc3fQ.DGY4VsOt0lKbkUf_3FzWTDl1_OuUtKlttbxTadzn9eU
charset: utf-8
app-model: M2012K11AC
app-system: Android 13
app-path: /pages/user/user
User-Agent: Mozilla/5.0 (Linux; Android 13; M2012K11AC Build/TKQ1.221114.001; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/138.0.7204.180 Mobile Safari/537.36 XWEB/1380085 MMWEBSDK/20240404 MMWEBID/4824 MicroMessenger/8.0.49.2600(0x28003133) WeChat/arm64 Weixin NetType/WIFI Language/zh_CN ABI/arm64 MiniProgramEnv/android
content-type: application/json
Accept-Encoding: gzip,compress,br,deflate
app-sdkversion: 3.4.10
app-version: 8.0.49
Referer: https://servicewechat.com/wxff438d3c60c63fb6/342/page-frame.html



HTTP/1.1 200 OK
Date: Tue, 05 Aug 2025 08:41:00 GMT
Content-Type: application/json; charset=utf-8
Content-Length: 31
Connection: keep-alive
X-Request-Id: ecb5c001e8a1d844172f727e5af0f2e4
Access-Control-Allow-Origin: *
Access-Control-Allow-Credentials: true

{"code":0,"data":[],"msg":"ok"}