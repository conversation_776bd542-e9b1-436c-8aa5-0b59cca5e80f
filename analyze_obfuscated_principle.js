/**
 * 分析混淆代码的原理 - 理解数组与token的对应关系
 */

console.log('=== 分析混淆代码原理 ===\n');

// 最新的混淆代码数据
const NEW_XOR_ARRAY = [600, 3427, 3559, 274, 2525];
const OLD_XOR_ARRAY = [2212, 53, 2852, 3445, 3327];

console.log(`新XOR数组: [${NEW_XOR_ARRAY.join(', ')}]`);
console.log(`旧XOR数组: [${OLD_XOR_ARRAY.join(', ')}]`);
console.log(`目标token: 7jdcpm\n`);

// 分析混淆代码的结构
function analyzeObfuscatedStructure() {
    console.log('1. 分析混淆代码结构:');
    
    // 从日志中提取的混淆代码片段
    const codeStructure = `
    var __gmibIrDac = String.fromCharCode;
    var _x_ZVm = [600, 3427, 3559, 274, 2525];
    var _$YJ50 = function(x) { return x ^ _x_ZVm[0]; };
    var _$ZTn = function(x) { return x ^ _x_ZVm[1]; };
    var _$OhV = function(x) { return x ^ _x_ZVm[2]; };
    var _$tMg = function(x) { return x ^ _x_ZVm[3]; };
    var _$ShdX = function(x) { return x ^ _x_ZVm[4]; };
    eval(__gmibIrDac(32) + __gmibIrDac(...) + ...);
    `;
    
    console.log('代码结构:');
    console.log('- __gmibIrDac = String.fromCharCode (字符生成函数)');
    console.log('- _x_ZVm = XOR数组');
    console.log('- 5个XOR函数，分别对应数组的5个元素');
    console.log('- 最后通过eval执行动态生成的字符串\n');
    
    return {
        charFunction: '__gmibIrDac',
        xorArray: '_x_ZVm',
        xorFunctions: ['_$YJ50', '_$ZTn', '_$OhV', '_$tMg', '_$ShdX']
    };
}

// 分析eval中的参数模式
function analyzeEvalParameters() {
    console.log('2. 分析eval参数模式:');
    
    // 从日志中提取的eval调用示例
    const evalExamples = [
        '__gmibIrDac(32)',
        '__gmibIrDac(-1-~(0x77^0), 105, 110, 0x6477>>4>>4, 111, ~119, _$ShdX(2438), ...)',
        '_$YJ50(6219/0xA)',
        '_$ZTn(3408)',
        '_$OhV(3516)',
        '_$tMg(0x174)',
        '_$ShdX(2438)'
    ];
    
    console.log('eval参数模式:');
    evalExamples.forEach((example, index) => {
        console.log(`  ${index + 1}. ${example}`);
    });
    
    console.log('\n关键发现:');
    console.log('- eval通过String.fromCharCode生成字符');
    console.log('- 参数包含直接数值和XOR函数调用');
    console.log('- XOR函数的参数是各种数学表达式');
    console.log('- 最终生成的字符串会被eval执行\n');
}

// 尝试手动计算一些XOR函数的结果
function calculateXORResults() {
    console.log('3. 计算XOR函数结果:');
    
    const xorArray = NEW_XOR_ARRAY;
    
    // 定义XOR函数
    const _$YJ50 = (x) => x ^ xorArray[0];  // x ^ 600
    const _$ZTn = (x) => x ^ xorArray[1];   // x ^ 3427
    const _$OhV = (x) => x ^ xorArray[2];   // x ^ 3559
    const _$tMg = (x) => x ^ xorArray[3];   // x ^ 274
    const _$ShdX = (x) => x ^ xorArray[4];  // x ^ 2525
    
    console.log('XOR函数计算示例:');
    
    // 计算一些示例值
    const testValues = [100, 200, 300, 400, 500, 1000, 2000, 3000];
    
    testValues.forEach(val => {
        const results = [
            _$YJ50(val),
            _$ZTn(val),
            _$OhV(val),
            _$tMg(val),
            _$ShdX(val)
        ];
        
        console.log(`  输入${val}: [${results.join(', ')}]`);
        
        // 检查是否有结果在ASCII可打印范围内 (32-126)
        const printableResults = results.filter(r => r >= 32 && r <= 126);
        if (printableResults.length > 0) {
            const chars = printableResults.map(r => String.fromCharCode(r));
            console.log(`    -> 可打印字符: [${chars.join(', ')}]`);
        }
    });
    
    console.log('');
}

// 分析目标token的ASCII值与XOR数组的关系
function analyzeTokenASCIIRelation() {
    console.log('4. 分析目标token与XOR数组的关系:');
    
    const targetToken = '7jdcpm';
    const targetASCII = targetToken.split('').map(c => c.charCodeAt(0));
    
    console.log(`目标token: ${targetToken}`);
    console.log(`ASCII值: [${targetASCII.join(', ')}]`);
    
    // 尝试找到XOR关系
    console.log('\n寻找XOR关系:');
    
    targetASCII.forEach((ascii, index) => {
        console.log(`\n位置${index}: '${targetToken[index]}' (ASCII ${ascii})`);
        
        // 尝试与每个XOR数组元素进行XOR
        NEW_XOR_ARRAY.forEach((xorVal, xorIndex) => {
            const result = ascii ^ xorVal;
            console.log(`  ${ascii} ^ ${xorVal} = ${result}`);
            
            // 检查结果是否有意义
            if (result >= 0 && result <= 10000) {
                // 检查是否是常见的数学表达式结果
                const commonValues = [32, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57]; // 空格和数字
                if (commonValues.includes(result)) {
                    console.log(`    -> 可能的输入值: ${result} (常见值)`);
                }
            }
        });
    });
}

// 尝试逆向工程eval的参数
function reverseEngineerEvalParams() {
    console.log('\n5. 逆向工程eval参数:');
    
    const targetToken = '7jdcpm';
    const targetASCII = [55, 106, 100, 99, 112, 109]; // 7jdcpm的ASCII值
    
    console.log('如果eval最终生成的是token字符，那么:');
    console.log(`String.fromCharCode(${targetASCII.join(', ')}) = "${targetToken}"`);
    
    console.log('\n寻找生成这些ASCII值的XOR表达式:');
    
    const xorArray = NEW_XOR_ARRAY;
    
    targetASCII.forEach((ascii, index) => {
        console.log(`\n目标ASCII ${ascii} (字符'${targetToken[index]}'):`);
        
        // 尝试找到XOR表达式: input ^ xorArray[i] = ascii
        // 即: input = ascii ^ xorArray[i]
        xorArray.forEach((xorVal, xorIndex) => {
            const input = ascii ^ xorVal;
            console.log(`  如果使用XOR[${xorIndex}](${xorVal}): 输入值应为 ${input}`);
            
            // 检查这个输入值是否合理
            if (input >= 0 && input <= 10000) {
                // 检查是否是简单的数学表达式
                if (input < 256) {
                    console.log(`    -> 可能的直接值: ${input}`);
                } else {
                    // 检查是否是常见的数学表达式结果
                    const factors = [];
                    for (let i = 2; i <= Math.sqrt(input); i++) {
                        if (input % i === 0) {
                            factors.push([i, input / i]);
                        }
                    }
                    if (factors.length > 0) {
                        console.log(`    -> 可能的表达式: ${factors.map(f => `${f[0]}*${f[1]}`).join(' 或 ')}`);
                    }
                }
            }
        });
    });
}

// 主分析函数
function main() {
    const structure = analyzeObfuscatedStructure();
    analyzeEvalParameters();
    calculateXORResults();
    analyzeTokenASCIIRelation();
    reverseEngineerEvalParams();
    
    console.log('\n=== 分析结论 ===');
    console.log('1. 混淆代码通过eval执行动态生成的JavaScript代码');
    console.log('2. 代码最终会生成token字符串');
    console.log('3. XOR数组用于解密eval参数中的数值');
    console.log('4. 我们需要正确解析eval中的所有参数才能得到真正的token');
    console.log('5. 固定返回"7jdcpm"是错误的，应该动态计算');
    
    console.log('\n下一步:');
    console.log('1. 完整解析eval中的所有参数');
    console.log('2. 正确计算XOR表达式');
    console.log('3. 生成最终的token字符串');
}

// 运行分析
main();
