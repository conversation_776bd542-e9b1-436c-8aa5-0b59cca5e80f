/**
 * 直接测试：用抓包数据验证我们的方法
 */

console.log('=== 直接验证测试 ===\n');

// 从抓包文件中提取的实际混淆代码（不完整，但包含关键部分）
const CAPTURED_CODE = `/*qNVOMRa*/var/*RyR687XEI83CJb8fHR*/__qxBY2hC/*YMXkbo*/=\\u0053\\u0074\\u0072\\u0069\\u006e\\u0067
/*lXX1kbkRXJ9FmZqK*/./*MZCeD*/\\u0066r\\u006fm\\u0043ha\\u0072C\\u006fde/*sMmtyNpcAyql8O*/;
var/*Cfhx2UMXDK6BMZM*/_x_sEd = [/*dF4tvpacOzsnV9*/2212,53,2852,3445,3327,/*SPaJmEBdtdDh0xIQ5b*/];//vYnpXz6vIEdS5RW
var/*bcNoFOVIxVdidN*/_$3vge/*TLSE8sLsN11d*/=/*WloJg5inrhui0kzNK*/function(/*IzKb6MJKvAxCI*/){
/*4XMcxf8q6w5Eu9E8*/return/*tjsXXWm*/arguments[/*7KLqBRABhc6cioDsfg*/0]^/*NkiyNuHr*/
/*PX3neKSxLULiZ3uE*/_x_sEd[/*oZo7hG3hKugxQh6c5Sh*/0];/*Gy6Av9AnY5HTsFbp*/}/*QeINbNufqrnfY*/;`;

// 期望的结果
const EXPECTED_TOKEN = '7jdcpm';

console.log(`期望token: ${EXPECTED_TOKEN}`);
console.log(`抓包代码长度: ${CAPTURED_CODE.length}\n`);

// 测试1: 我们当前的VM方法
function testCurrentVMMethod() {
    console.log('1. 测试当前VM方法:');
    
    try {
        const vm = require('vm');
        let allTokens = [];
        
        const sandbox = {
            String: String,
            Math: Math,
            parseInt: parseInt,
            parseFloat: parseFloat,
            window: {},
            console: {
                log: (...args) => {
                    console.log('VM输出:', ...args);
                    args.forEach(arg => {
                        if (typeof arg === 'string' && /^[a-z0-9]{6,12}$/.test(arg)) {
                            if (!allTokens.includes(arg)) {
                                allTokens.push(arg);
                            }
                        }
                    });
                }
            },
            eval: (code) => {
                try {
                    console.log(`执行eval: ${code.substring(0, 100)}...`);
                    const result = vm.runInContext(code, sandbox, { timeout: 5000 });
                    
                    if (sandbox.window) {
                        Object.keys(sandbox.window).forEach(key => {
                            const value = sandbox.window[key];
                            if (typeof value === 'string' && /^[a-z0-9]{6,12}$/.test(value)) {
                                if (!allTokens.includes(value)) {
                                    allTokens.push(value);
                                }
                            }
                        });
                    }
                    
                    return result;
                } catch (e) {
                    console.log(`eval失败: ${e.message}`);
                    return undefined;
                }
            }
        };
        
        vm.createContext(sandbox);
        vm.runInContext(CAPTURED_CODE, sandbox, { timeout: 10000 });
        
        console.log(`VM方法结果: ${allTokens.length > 0 ? allTokens.join(', ') : '无token'}`);
        console.log(`是否包含期望token: ${allTokens.includes(EXPECTED_TOKEN) ? '✅' : '❌'}`);
        
        return allTokens;
        
    } catch (error) {
        console.log(`VM方法失败: ${error.message}`);
        return [];
    }
}

// 测试2: 手动构建执行环境
function testManualExecution() {
    console.log('\n2. 测试手动执行:');
    
    try {
        // 手动构建环境
        const _x_sEd = [2212, 53, 2852, 3445, 3327];
        const __qxBY2hC = String.fromCharCode;
        const _$3vge = (x) => x ^ _x_sEd[0];
        
        console.log(`XOR数组: [${_x_sEd.join(', ')}]`);
        console.log(`__qxBY2hC = String.fromCharCode`);
        console.log(`_$3vge(x) = x ^ ${_x_sEd[0]}`);
        
        // 如果混淆代码最终是要生成 "7jdcpm"
        // 那么我们需要找到生成这些字符的输入值
        
        const targetASCII = EXPECTED_TOKEN.split('').map(c => c.charCodeAt(0));
        console.log(`目标ASCII: [${targetASCII.join(', ')}]`);
        
        // 尝试直接生成
        const directToken = __qxBY2hC(...targetASCII);
        console.log(`直接生成: ${directToken}`);
        console.log(`匹配: ${directToken === EXPECTED_TOKEN ? '✅' : '❌'}`);
        
        // 尝试通过XOR生成
        console.log('\n尝试XOR生成:');
        targetASCII.forEach((ascii, index) => {
            const input = ascii ^ _x_sEd[0]; // 使用第一个XOR值
            const result = _$3vge(input);
            console.log(`位置${index}: 输入${input} -> XOR结果${result} -> 字符'${String.fromCharCode(result)}' ${result === ascii ? '✅' : '❌'}`);
        });
        
        return directToken;
        
    } catch (error) {
        console.log(`手动执行失败: ${error.message}`);
        return null;
    }
}

// 测试3: 分析抓包代码是否完整
function analyzeCodeCompleteness() {
    console.log('\n3. 分析代码完整性:');
    
    // 检查代码结构
    const hasStringFromCharCode = CAPTURED_CODE.includes('\\u0053\\u0074\\u0072\\u0069\\u006e\\u0067');
    const hasXORArray = CAPTURED_CODE.includes('_x_sEd');
    const hasXORFunction = CAPTURED_CODE.includes('_$3vge');
    const hasEval = CAPTURED_CODE.includes('\\u0065\\u0076\\u0061\\u006c');
    
    console.log(`包含String.fromCharCode: ${hasStringFromCharCode ? '✅' : '❌'}`);
    console.log(`包含XOR数组: ${hasXORArray ? '✅' : '❌'}`);
    console.log(`包含XOR函数: ${hasXORFunction ? '✅' : '❌'}`);
    console.log(`包含eval调用: ${hasEval ? '❌ (代码被截断)' : '❌'}`);
    
    // 检查代码是否被截断
    const endsWithSemicolon = CAPTURED_CODE.trim().endsWith(';');
    const endsWithBrace = CAPTURED_CODE.trim().endsWith('}');
    
    console.log(`代码结构完整: ${endsWithSemicolon || endsWithBrace ? '可能完整' : '❌ 被截断'}`);
    
    if (!hasEval) {
        console.log('\n⚠️ 关键发现: 抓包代码缺少eval部分！');
        console.log('这解释了为什么我们无法复现token。');
        console.log('eval部分包含了真正的token生成逻辑。');
    }
}

// 测试4: 假设token是固定的
function testFixedTokenHypothesis() {
    console.log('\n4. 测试固定token假设:');
    
    // 如果token真的是固定的 "7jdcpm"
    console.log('假设1: token是固定值');
    console.log(`固定token: ${EXPECTED_TOKEN}`);
    console.log('这种情况下，混淆代码只是为了隐藏这个固定值');
    
    // 检查是否能在代码中找到相关的ASCII值
    const targetASCII = [55, 106, 100, 99, 112, 109]; // 7jdcpm
    console.log(`目标ASCII值: [${targetASCII.join(', ')}]`);
    
    let foundASCII = 0;
    targetASCII.forEach(ascii => {
        if (CAPTURED_CODE.includes(ascii.toString())) {
            foundASCII++;
        }
    });
    
    console.log(`代码中找到的ASCII值: ${foundASCII}/${targetASCII.length}`);
    
    if (foundASCII === 0) {
        console.log('❌ 代码中没有直接的ASCII值，token不是简单的固定值');
    } else {
        console.log('✅ 可能包含相关的ASCII值');
    }
}

// 主测试函数
function main() {
    const vmTokens = testCurrentVMMethod();
    const manualToken = testManualExecution();
    analyzeCodeCompleteness();
    testFixedTokenHypothesis();
    
    console.log('\n=== 最终结论 ===');
    
    if (vmTokens.includes(EXPECTED_TOKEN)) {
        console.log('✅ VM方法成功复现了期望token');
    } else if (manualToken === EXPECTED_TOKEN) {
        console.log('✅ 手动方法成功复现了期望token');
    } else {
        console.log('❌ 所有方法都无法复现期望token');
        console.log('\n可能的原因:');
        console.log('1. 抓包代码不完整（缺少eval部分）');
        console.log('2. token生成依赖服务器端参数');
        console.log('3. 我们对混淆逻辑的理解有误');
        console.log('4. token不是通过这段代码生成的');
        
        console.log('\n建议:');
        console.log('1. 获取完整的混淆代码（包括eval部分）');
        console.log('2. 分析更多的抓包数据');
        console.log('3. 考虑token可能来自其他来源');
    }
}

// 运行测试
main();
