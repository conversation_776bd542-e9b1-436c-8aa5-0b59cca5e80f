# -*- coding: utf-8 -*-
import os

# 测试URL（重定向后的URL）
test_url = "https://mcs-mimp-web.sf-express.com/up-member/newHome?mobile=181****5357&userId=73FE37E7FC644FF5BD7709C54B895007&path=/up-member/newHome&supportShare=NO&maskType=autoReceive&from=surprise_benefitwxauto&equityKey=surprise_benefit&citycode=833&cityname=乐山"

# 设置环境变量
os.environ['sfsyUrl'] = test_url

print("=" * 60)
print("测试修改后的URL分割逻辑")
print("=" * 60)
print(f"测试URL: {test_url}")
print("=" * 60)

# 运行脚本
if __name__ == "__main__":
    exec(open('顺丰1.1.py', encoding='utf-8').read())
