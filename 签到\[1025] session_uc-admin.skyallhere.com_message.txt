GET /api/userCouponInfo/getUserCouponNum HTTP/1.1
Host: uc-admin.skyallhere.com
Connection: keep-alive
isNoToken: 1
App-Path: /pages/user/user
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJvcGVuaWQiOiJvTVNTZzRtR2dnc0hNUWp0TmdBVXNYLVJ1aUpzIiwidW5pb25pZCI6Im9BdWVHamtzTHdieFRPY2tGejAzS2I5NWExTzgiLCJ1c2VyaWQiOjE5NDYwMzc5LCJ1c2VyX2NvZGUiOiIxYWI4ODgxNGNjNDhmNzdmIiwidXNlcl9waG9uZSI6IjE4ODA4MzMzMzk2Iiwibmlja19uYW1lIjoiIiwiZXhwIjoxNzU0Mzc2NTMxfQ.rOAXDkwcQHswBIS6JH1UdvG4cjYwj7926DvSvfd1_dM
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF WindowsWechat(0x63090c37)XWEB/14315
Content-Type: application/json
App-System: Windows 10 x64
xweb_xhr: 1
App-Model: microsoft
App-Sdkversion: 3.9.0
App-Version: 3.9.12
Accept: */*
Sec-Fetch-Site: cross-site
Sec-Fetch-Mode: cors
Sec-Fetch-Dest: empty
Referer: https://servicewechat.com/wxff438d3c60c63fb6/342/page-frame.html
Accept-Encoding: gzip, deflate, br
Accept-Language: zh-CN,zh;q=0.9



HTTP/1.1 200
Date: Tue, 05 Aug 2025 06:19:01 GMT
Content-Type: application/json
Transfer-Encoding: chunked
Connection: keep-alive
Access-Control-Allow-Origin: *
Access-Control-Allow-Credentials: true

{"code":"200","data":0,"msg":"ok","success":true}