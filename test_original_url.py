# -*- coding: utf-8 -*-
import os

# 测试原始重定向URL
original_url = "https://mcs-mimp-web.sf-express.com/mcs-mimp/share/weChat/activityRedirect?source=CX&unionId=YQ1N3lUDNaSg9Ox9%2BBwNXRFFsW2OBAKilgqhG8pgOBo%3D&openId=s1G5ik6AWFIQQaL5sshp%2FZwTIdtITonvNlva7jmu380%3D&memId=hych5uZiPmTZK%2FpoxrKOmetUH%2BV8uGn%2F%2FYU88EDDho4DdjSr%2F6X0WoiZtgGzs7sG&memNo=6tB15T6k0ZvyroDMcHJm6TxGNOheCN75HuG7rOc5Fw4DdjSr%2F6X0WoiZtgGzs7sG&mobile=EpJ84tf34s%2B0ot4Y2kXwuA%3D%3D&mediaCode=wxapp&bizCode=%7B%22path%22%3A%22%2Fup-member%2FnewHome%22%2C%22supportShare%22%3A%22NO%22%2C%22maskType%22%3A%22autoReceive%22%2C%22from%22%3A%22surprise_benefitwxauto%22%2C%22equityKey%22%3A%22surprise_benefit%22%7D&citycode=833&cityname=%E4%B9%90%E5%B1%B1&cx-at-sign=BA3A58B406AF0580728038A1FE486B2C4D52A13288EE6057715F83DD26392367&cx-at-ts=1754409232&cx-at-nonce=act0zf_9CAvIYpebqBICe&t=1754409232"

# 设置环境变量
os.environ['sfsyUrl'] = original_url

print("=" * 60)
print("测试原始重定向URL")
print("=" * 60)
print(f"测试URL: {original_url[:100]}...")
print("=" * 60)

# 运行脚本
if __name__ == "__main__":
    exec(open('顺丰1.1.py', encoding='utf-8').read())
