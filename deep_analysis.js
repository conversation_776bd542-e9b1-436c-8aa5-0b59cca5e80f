/**
 * 深度分析混淆代码
 */

// 完整的混淆代码（从抓包文件中提取）
const fullObfuscatedCode = `/*qNVOMRa*/var/*RyR687XEI83CJb8fHR*/__qxBY2hC/*YMXkbo*/=\\u0053\\u0074\\u0072\\u0069\\u006e\\u0067
/*lXX1kbkRXJ9FmZqK*/./*MZCeD*/\\u0066r\\u006fm\\u0043ha\\u0072C\\u006fde/*sMmtyNpcAyql8O*/;
var/*Cfhx2UMXDK6BMZM*/_x_sEd = [/*dF4tvpacOzsnV9*/2212,53,2852,3445,3327,/*SPaJmEBdtdDh0xIQ5b*/];//vYnpXz6vIEdS5RW
var/*bcNoFOVIxVdidN*/_$3vge/*TLSE8sLsN11d*/=/*WloJg5inrhui0kzNK*/function(/*IzKb6MJKvAxCI*/){
/*4XMcxf8q6w5Eu9E8*/return/*tjsXXWm*/arguments[/*7KLqBRABhc6cioDsfg*/0]^/*NkiyNuHr*/
/*PX3neKSxLULiZ3uE*/_x_sEd[/*oZo7hG3hKugxQh6c5Sh*/0];/*Gy6Av9AnY5HTsFbp*/}/*QeINbNufqrnfY*/;
var/*SIFKkOuBNUnJlDrM*/_$2ez/*yfzoPOI8Fib00OELrm2*/=/*9w8TNqsIoLCi*/function(/*iN7pPA*/){
/*NRSvRnrefQJGwlSKF*/return/*hlpVATricR4QI*/arguments[/*TKhZPT*/0]^/*DcyqzBmfWtYZtrYq*/
/*ephneKlI5Klj3XaY*/_x_sEd[/*xQwiYQhHAIcVBl*/2];/*VaVwcP0KsBCb*/}/*O2AbLE3*/;
var/*EBCmqPRXJjw963BUW*/_$zcp/*FIxtMxvs7*/=/*hiQYBLvx4Q*/function(/*blmZnKgp9lNT*/){
/*OpFi3sgXXE4yRwWQtmg*/return/*UyuXTyv3kqhAm*/arguments[/*HOROTkaqm0n*/0]^/*YVuXf7JfY*/
/*dHvhIDpEqH*/_x_sEd[/*u3RnTzJIop*/3];/*dp2w9GwOVNCg6N*/}/*skQDPME88ahZdMrfaO*/;
var/*ZUvScLDkqPit2O*/_$mRym/*KPYaqzLzv1*/=/*RyJFl*/function(/*vj7hxeNNgVoNSQRv*/){
/*FpAZa7H*/return/*CLoUkWelj8eJ*/arguments[/*oQdReQyvmxXvuP*/0]^/*ExXATHEo*/
/*oUPGWDQRtI0aOZS*/_x_sEd[/*iqgXP7CzR0*/4];/*GPHHeemz7ZD54*/}/*jg0tDHN72hX*/;`;

console.log('=== 深度分析混淆代码 ===\n');

// 1. 清理和解析代码
function cleanObfuscatedCode(code) {
    console.log('1. 清理混淆代码...');
    
    // 移除注释
    let cleaned = code.replace(/\/\*[^*]*\*\//g, '');
    
    // 解码Unicode字符
    cleaned = cleaned.replace(/\\u([0-9a-fA-F]{4})/g, (match, hex) => {
        return String.fromCharCode(parseInt(hex, 16));
    });
    
    console.log('清理后的代码:');
    console.log(cleaned);
    console.log('');
    
    return cleaned;
}

// 2. 提取关键信息
function extractKeyInfo(code) {
    console.log('2. 提取关键信息...');
    
    // 提取数组
    const arrayMatch = code.match(/_x_sEd\s*=\s*\[([^\]]+)\]/);
    if (arrayMatch) {
        const arrayStr = arrayMatch[1];
        const numbers = arrayStr.split(',').map(s => parseInt(s.trim())).filter(n => !isNaN(n));
        console.log(`XOR数组: [${numbers.join(', ')}]`);
    }
    
    // 提取函数定义
    const funcMatches = code.match(/var\s*(_\$\w+)\s*=\s*function[^}]+}/g);
    if (funcMatches) {
        console.log('函数定义:');
        funcMatches.forEach((func, index) => {
            console.log(`  函数${index + 1}: ${func}`);
        });
    }
    
    // 提取主变量
    const mainVarMatch = code.match(/var\s*(__\w+)\s*=\s*String/);
    if (mainVarMatch) {
        console.log(`主变量: ${mainVarMatch[1]} = String.fromCharCode`);
    }
    
    console.log('');
}

// 3. 尝试手动执行逻辑
function manualExecution() {
    console.log('3. 手动执行逻辑...');
    
    // 根据清理后的代码手动构建执行环境
    const _x_sEd = [2212, 53, 2852, 3445, 3327];
    const __qxBY2hC = String.fromCharCode;
    
    const _$3vge = function(x) { return x ^ _x_sEd[0]; };
    const _$2ez = function(x) { return x ^ _x_sEd[2]; };
    const _$zcp = function(x) { return x ^ _x_sEd[3]; };
    const _$mRym = function(x) { return x ^ _x_sEd[4]; };
    
    console.log('构建的执行环境:');
    console.log(`_x_sEd: [${_x_sEd.join(', ')}]`);
    console.log(`__qxBY2hC: String.fromCharCode`);
    console.log(`_$3vge(100): ${_$3vge(100)}`);
    console.log(`_$2ez(100): ${_$2ez(100)}`);
    console.log(`_$zcp(100): ${_$zcp(100)}`);
    console.log(`_$mRym(100): ${_$mRym(100)}`);
    console.log('');
}

// 4. 尝试不同的token生成策略
function tryDifferentStrategies() {
    console.log('4. 尝试不同的token生成策略...');
    
    const timestamp = 1754383264519;
    const target = '7jdcpm';
    
    // 策略1: 基于时间戳的简单映射
    function strategy1() {
        const chars = 'abcdefghijklmnopqrstuvwxyz0123456789';
        const timeStr = timestamp.toString();
        let result = '';
        
        for (let i = 0; i < 6; i++) {
            const charCode = timeStr.charCodeAt(i % timeStr.length);
            const index = charCode % chars.length;
            result += chars[index];
        }
        
        return result;
    }
    
    // 策略2: 基于时间戳的MD5
    function strategy2() {
        const crypto = require('crypto');
        const hash = crypto.createHash('md5').update(timestamp.toString()).digest('hex');
        
        // 尝试不同的截取方式
        const variants = [
            hash.substring(0, 6),
            hash.substring(8, 14),
            hash.substring(16, 22),
            hash.substring(26, 32)
        ];
        
        return variants;
    }
    
    // 策略3: 基于时间戳的特殊编码
    function strategy3() {
        const chars = '0123456789abcdefghijklmnopqrstuvwxyz';
        const timeStr = timestamp.toString();
        let result = '';
        
        // 使用时间戳的不同部分
        const parts = [
            parseInt(timeStr.substring(0, 4)),   // 年份部分
            parseInt(timeStr.substring(4, 8)),   // 中间部分
            parseInt(timeStr.substring(8, 12)),  // 后面部分
        ];
        
        parts.forEach(part => {
            const index1 = part % chars.length;
            const index2 = Math.floor(part / chars.length) % chars.length;
            result += chars[index1] + chars[index2];
        });
        
        return result;
    }
    
    console.log(`目标token: ${target}`);
    console.log(`策略1 (字符码映射): ${strategy1()}`);
    console.log(`策略2 (MD5变体): ${strategy2().join(', ')}`);
    console.log(`策略3 (特殊编码): ${strategy3()}`);
    console.log('');
}

// 5. 逆向分析目标token
function reverseAnalyzeTarget() {
    console.log('5. 逆向分析目标token...');
    
    const target = '7jdcpm';
    const chars = 'abcdefghijklmnopqrstuvwxyz0123456789';
    
    console.log(`目标token: ${target}`);
    console.log('字符分析:');
    
    for (let i = 0; i < target.length; i++) {
        const char = target[i];
        const index = chars.indexOf(char);
        const charCode = char.charCodeAt(0);
        
        console.log(`  位置${i}: '${char}' -> 索引${index}, ASCII${charCode}`);
    }
    
    // 查看是否有规律
    const indices = target.split('').map(c => chars.indexOf(c));
    console.log(`索引序列: [${indices.join(', ')}]`);
    
    // 检查与时间戳的关系
    const timestamp = 1754383264519;
    const timeStr = timestamp.toString();
    
    console.log(`时间戳: ${timestamp}`);
    console.log(`时间戳字符串: ${timeStr}`);
    
    for (let i = 0; i < target.length; i++) {
        const targetIndex = indices[i];
        const timeChar = timeStr[i % timeStr.length];
        const timeValue = parseInt(timeChar);
        
        console.log(`  位置${i}: 目标索引${targetIndex}, 时间字符'${timeChar}'(${timeValue})`);
    }
    
    console.log('');
}

// 主函数
function main() {
    const cleaned = cleanObfuscatedCode(fullObfuscatedCode);
    extractKeyInfo(cleaned);
    manualExecution();
    tryDifferentStrategies();
    reverseAnalyzeTarget();
    
    console.log('=== 结论 ===');
    console.log('我们的算法无法复现实际token，可能的原因:');
    console.log('1. 混淆代码中还有我们未发现的逻辑');
    console.log('2. token生成可能依赖服务器端的其他参数');
    console.log('3. 可能使用了不同的字符集或编码方式');
    console.log('4. 时间戳可能不是唯一的输入参数');
}

main();
