# -*- coding: utf-8 -*-
from urllib.parse import urlparse, parse_qs, unquote
import json

def analyze_sf_url():
    """分析顺丰URL中的关键参数"""
    test_url = "https://mcs-mimp-web.sf-express.com/mcs-mimp/share/weChat/activityRedirect?source=CX&unionId=YQ1N3lUDNaSg9Ox9%2BBwNXRFFsW2OBAKilgqhG8pgOBo%3D&openId=s1G5ik6AWFIQQaL5sshp%2FZwTIdtITonvNlva7jmu380%3D&memId=hych5uZiPmTZK%2FpoxrKOmetUH%2BV8uGn%2F%2FYU88EDDho4DdjSr%2F6X0WoiZtgGzs7sG&memNo=6tB15T6k0ZvyroDMcHJm6TxGNOheCN75HuG7rOc5Fw4DdjSr%2F6X0WoiZtgGzs7sG&mobile=EpJ84tf34s%2B0ot4Y2kXwuA%3D%3D&mediaCode=wxapp&bizCode=%7B%22path%22%3A%22%2Fup-member%2FnewHome%22%2C%22supportShare%22%3A%22NO%22%2C%22maskType%22%3A%22autoReceive%22%2C%22from%22%3A%22surprise_benefitwxauto%22%2C%22equityKey%22%3A%22surprise_benefit%22%7D&citycode=833&cityname=%E4%B9%90%E5%B1%B1&cx-at-sign=C6E170041AF7782DC61DDEDD46E8C24CA3A0D1D4AD24CD5D0505763F8F4E0FA7&cx-at-ts=1754409196&cx-at-nonce=qgAzD0Sotk60w19Okw3CV&t=1754409196"
    
    print("🔍 分析顺丰URL参数结构")
    print("=" * 60)
    
    # 解析URL
    parsed_url = urlparse(test_url)
    params = parse_qs(parsed_url.query)
    
    print(f"🌐 域名: {parsed_url.netloc}")
    print(f"📍 路径: {parsed_url.path}")
    print()
    
    print("📋 URL参数详细分析:")
    print("-" * 40)
    
    # 关键参数分析
    key_params = {
        'source': '来源标识',
        'unionId': '微信UnionID (加密)',
        'openId': '微信OpenID (加密)', 
        'memId': '会员ID (加密)',
        'memNo': '会员编号 (加密)',
        'mobile': '手机号 (加密)',
        'mediaCode': '媒体代码',
        'bizCode': '业务代码 (JSON)',
        'citycode': '城市代码',
        'cityname': '城市名称',
        'cx-at-sign': '签名参数',
        'cx-at-ts': '时间戳',
        'cx-at-nonce': '随机数',
        't': '时间戳'
    }
    
    for param, description in key_params.items():
        if param in params:
            value = params[param][0]
            if param == 'bizCode':
                try:
                    # 解码bizCode JSON
                    decoded_biz = unquote(value)
                    biz_json = json.loads(decoded_biz)
                    print(f"📌 {param:15} | {description:20} | {decoded_biz}")
                    print(f"   └─ 解析JSON: {json.dumps(biz_json, indent=6, ensure_ascii=False)}")
                except:
                    print(f"📌 {param:15} | {description:20} | {value}")
            elif param in ['cityname']:
                decoded_value = unquote(value)
                print(f"📌 {param:15} | {description:20} | {decoded_value}")
            else:
                print(f"📌 {param:15} | {description:20} | {value}")
        else:
            print(f"❌ {param:15} | {description:20} | 未找到")
    
    print()
    print("🔐 登录认证相关参数:")
    print("-" * 40)
    
    # 登录认证关键参数
    auth_params = ['unionId', 'openId', 'memId', 'memNo', 'mobile']
    for param in auth_params:
        if param in params:
            value = params[param][0]
            print(f"🔑 {param:10} | {value}")
    
    print()
    print("⏰ 时间和签名参数:")
    print("-" * 40)
    
    # 时间签名参数
    time_params = ['cx-at-sign', 'cx-at-ts', 'cx-at-nonce', 't']
    for param in time_params:
        if param in params:
            value = params[param][0]
            if param in ['cx-at-ts', 't']:
                import datetime
                try:
                    timestamp = int(value)
                    dt = datetime.datetime.fromtimestamp(timestamp)
                    print(f"⏰ {param:12} | {value} ({dt.strftime('%Y-%m-%d %H:%M:%S')})")
                except:
                    print(f"⏰ {param:12} | {value}")
            else:
                print(f"🔐 {param:12} | {value}")
    
    print()
    print("💡 脚本登录逻辑分析:")
    print("-" * 40)
    print("1. 脚本直接使用完整URL进行GET请求")
    print("2. 服务器通过URL参数验证用户身份")
    print("3. 成功后服务器设置cookies:")
    print("   - _login_user_id_: 用户ID")
    print("   - _login_mobile_: 手机号")
    print("4. 脚本从cookies中提取用户信息")
    print()
    print("🎯 关键发现:")
    print("- URL中的所有参数都是必需的")
    print("- 加密参数(unionId, openId, memId等)包含用户身份信息")
    print("- 时间戳和签名参数用于防重放攻击")
    print("- URL具有时效性，过期后无法使用")

if __name__ == '__main__':
    analyze_sf_url()
