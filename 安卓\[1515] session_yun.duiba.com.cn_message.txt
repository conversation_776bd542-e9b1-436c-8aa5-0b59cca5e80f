GET /h5/sign_custom/skins/normal-calendar-sign_190826/index.1d4324a7.css h2
host: yun.duiba.com.cn
sec-ch-ua-platform: "Android"
user-agent: Mozilla/5.0 (Linux; Android 13; M2012K11AC Build/TKQ1.221114.001; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/138.0.7204.180 Mobile Safari/537.36 XWEB/1380085 MMWEBSDK/20240404 MMWEBID/4824 MicroMessenger/8.0.49.2600(0x28003133) WeChat/arm64 Weixin NetType/WIFI Language/zh_CN ABI/arm64 miniProgram/wxff438d3c60c63fb6
sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Android WebView";v="138"
sec-ch-ua-mobile: ?1
accept: text/css,*/*;q=0.1
x-requested-with: com.tencent.mm
sec-fetch-site: cross-site
sec-fetch-mode: no-cors
sec-fetch-dest: style
sec-fetch-storage-access: active
referer: https://74367-1-activity.m.dexfu.cn/
accept-encoding: gzip, deflate, br, zstd
accept-language: zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7
priority: u=0



h2 200
last-modified: Thu, 13 Apr 2023 04:28:29 GMT
content-encoding: gzip
server: AliyunOSS
date: Sun, 15 Jun 2025 06:11:11 GMT
content-type: text/css
vary: Accept-Encoding
x-oss-request-id: 684E63FFD4938A3535660D37
x-oss-object-type: Normal
x-oss-hash-crc64ecma: 12353333324853519571
x-oss-storage-class: Standard
content-md5: cfcE2GrwiueC6d4U6g4xAQ==
x-oss-server-time: 69
content-length: 19356
accept-ranges: bytes
x-nws-log-uuid: 15631996565958213334
x-cache-lookup: Cache Hit
access-control-allow-origin: *
access-control-expose-headers: Content-Length,Range
access-control-allow-methods: GET,HEAD,OPTIONS
access-control-allow-headers: Content-Length,Range
cache-control: max-age=31536000

.normal-page[data-v-12368bbe],
.normal-page-content[data-v-12368bbe] {
  font-size: 0.32rem;
  width: 100%;
  background-color: #fff;
  position: relative;
}
.opacity-4[data-v-12368bbe] {
  opacity: 0.4;
}
.blur[data-v-12368bbe] {
  -webkit-filter: blur(0.066667rem);
  filter: blur(0.066667rem);
}
.icon[data-v-12368bbe] {
  width: 1em;
  height: 1em;
  vertical-align: -0.15em;
  fill: currentColor;
  overflow: hidden;
}
.scale-enter-active[data-v-12368bbe] {
  -webkit-transition: opacity 0.3s, -webkit-transform 0.3s;
  transition: opacity 0.3s, -webkit-transform 0.3s;
  -o-transition: opacity 0.3s, transform 0.3s;
  transition: opacity 0.3s, transform 0.3s;
  transition: opacity 0.3s, transform 0.3s, -webkit-transform 0.3s;
}
.scale-enter-active > div[data-v-12368bbe]:nth-of-type(1),
.scale-enter-active > div[data-v-12368bbe]:nth-of-type(2),
.scale-enter-active > section[data-v-12368bbe]:first-child {
  -webkit-animation: bounceIn-data-v-12368bbe 0.3s linear alternate both;
  animation: bounceIn-data-v-12368bbe 0.3s linear alternate both;
}
.scale-leave-active[data-v-12368bbe] {
  -webkit-transition: opacity 0.2s, -webkit-transform 0.2s;
  transition: opacity 0.2s, -webkit-transform 0.2s;
  -o-transition: opacity 0.2s, transform 0.2s;
  transition: opacity 0.2s, transform 0.2s;
  transition: opacity 0.2s, transform 0.2s, -webkit-transform 0.2s;
}
.scale-leave-active > div[data-v-12368bbe]:nth-of-type(1),
.scale-leave-active > div[data-v-12368bbe]:nth-of-type(2),
.scale-leave-active > section[data-v-12368bbe]:first-child {
  -webkit-animation: bounceOut-data-v-12368bbe 0.2s linear alternate both;
  animation: bounceOut-data-v-12368bbe 0.2s linear alternate both;
}
.scale-leave-to[data-v-12368bbe] {
  opacity: 0;
}
.scale-leave-to > div[data-v-12368bbe]:nth-of-type(1),
.scale-leave-to > div[data-v-12368bbe]:nth-of-type(2),
.scale-leave-to > section[data-v-12368bbe]:first-child {
  opacity: 0;
}
@-webkit-keyframes bounceIn-data-v-12368bbe {
0% {
    opacity: 0;
    -webkit-transform: scale(0.5);
    transform: scale(0.5);
}
100% {
    opacity: 1;
    -webkit-transform: scale(1);
    transform: scale(1);
}
}
@keyframes bounceIn-data-v-12368bbe {
0% {
    opacity: 0;
    -webkit-transform: scale(0.5);
    transform: scale(0.5);
}
100% {
    opacity: 1;
    -webkit-transform: scale(1);
    transform: scale(1);
}
}
@-webkit-keyframes bounceOut-data-v-12368bbe {
0% {
    opacity: 1;
    -webkit-transform: scale(1);
    transform: scale(1);
}
100% {
    opacity: 0;
    -webkit-transform: scale(0.5);
    transform: scale(0.5);
}
}
@keyframes bounceOut-data-v-12368bbe {
0% {
    opacity: 1;
    -webkit-transform: scale(1);
    transform: scale(1);
}
100% {
    opacity: 0;
    -webkit-transform: scale(0.5);
    transform: scale(0.5);
}
}
.banner[data-v-12368bbe] {
  width: 100%;
  height: 7.466667rem;
  background-size: 100% 100%;
}
.banner.app-5 .btn-right-wrap[data-v-12368bbe],
.banner.app-70711 .btn-right-wrap[data-v-12368bbe] {
  top: 1.146667rem;
}
.banner .btn-right-wrap[data-v-12368bbe] {
  position: absolute;
  width: 1.706667rem;
  height: 0.666667rem;
  top: 0.266667rem;
  right: 0;
  bottom: auto;
  left: auto;
}
.banner .rule[data-v-12368bbe],
.banner .record[data-v-12368bbe] {
  display: block;
  margin-bottom: 0.213333rem;
  text-align: center;
  line-height: 0.666667rem;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 0.4rem 0 0 0.4rem;
  padding-left: 0.053333rem;
  color: #fff;
}
.banner .credits[data-v-12368bbe] {
  position: absolute;
  width: auto;
  height: 0.586667rem;
  top: 0.266667rem;
  right: auto;
  bottom: auto;
  left: 0.266667rem;
  min-width: 1.946667rem;
  background: rgba(18, 26, 50, 0.8);
  border-radius: 0.293333rem;
  color: #fff;
  font-size: 0.346667rem;
  line-height: 0.48rem;
  text-align: center;
  padding: 0.053333rem 0.133333rem 0.053333rem 0.64rem;
  overflow: hidden;
  -webkit-mask-image: -webkit-radial-gradient(white, black);
}
.banner .credits-icon[data-v-12368bbe] {
  position: absolute;
  width: 0.48rem;
  height: 0.48rem;
  top: 0.053333rem;
  right: auto;
  bottom: auto;
  left: 0.053333rem;
  background-size: contain;
}
.normal-page[data-v-5b508c1a],
.normal-page-content[data-v-5b508c1a] {
  font-size: 0.32rem;
  width: 100%;
  background-color: #fff;
  position: relative;
}
.opacity-4[data-v-5b508c1a] {
  opacity: 0.4;
}
.blur[data-v-5b508c1a] {
  -webkit-filter: blur(0.066667rem);
  filter: blur(0.066667rem);
}
.icon[data-v-5b508c1a] {
  width: 1em;
  height: 1em;
  vertical-align: -0.15em;
  fill: currentColor;
  overflow: hidden;
}
.scale-enter-active[data-v-5b508c1a] {
  -webkit-transition: opacity 0.3s, -webkit-transform 0.3s;
  transition: opacity 0.3s, -webkit-transform 0.3s;
  -o-transition: opacity 0.3s, transform 0.3s;
  transition: opacity 0.3s, transform 0.3s;
  transition: opacity 0.3s, transform 0.3s, -webkit-transform 0.3s;
}
.scale-enter-active > div[data-v-5b508c1a]:nth-of-type(1),
.scale-enter-active > div[data-v-5b508c1a]:nth-of-type(2),
.scale-enter-active > section[data-v-5b508c1a]:first-child {
  -webkit-animation: bounceIn-data-v-5b508c1a 0.3s linear alternate both;
  animation: bounceIn-data-v-5b508c1a 0.3s linear alternate both;
}
.scale-leave-active[data-v-5b508c1a] {
  -webkit-transition: opacity 0.2s, -webkit-transform 0.2s;
  transition: opacity 0.2s, -webkit-transform 0.2s;
  -o-transition: opacity 0.2s, transform 0.2s;
  transition: opacity 0.2s, transform 0.2s;
  transition: opacity 0.2s, transform 0.2s, -webkit-transform 0.2s;
}
.scale-leave-active > div[data-v-5b508c1a]:nth-of-type(1),
.scale-leave-active > div[data-v-5b508c1a]:nth-of-type(2),
.scale-leave-active > section[data-v-5b508c1a]:first-child {
  -webkit-animation: bounceOut-data-v-5b508c1a 0.2s linear alternate both;
  animation: bounceOut-data-v-5b508c1a 0.2s linear alternate both;
}
.scale-leave-to[data-v-5b508c1a] {
  opacity: 0;
}
.scale-leave-to > div[data-v-5b508c1a]:nth-of-type(1),
.scale-leave-to > div[data-v-5b508c1a]:nth-of-type(2),
.scale-leave-to > section[data-v-5b508c1a]:first-child {
  opacity: 0;
}
@-webkit-keyframes bounceIn-data-v-5b508c1a {
0% {
    opacity: 0;
    -webkit-transform: scale(0.5);
    transform: scale(0.5);
}
100% {
    opacity: 1;
    -webkit-transform: scale(1);
    transform: scale(1);
}
}
@keyframes bounceIn-data-v-5b508c1a {
0% {
    opacity: 0;
    -webkit-transform: scale(0.5);
    transform: scale(0.5);
}
100% {
    opacity: 1;
    -webkit-transform: scale(1);
    transform: scale(1);
}
}
@-webkit-keyframes bounceOut-data-v-5b508c1a {
0% {
    opacity: 1;
    -webkit-transform: scale(1);
    transform: scale(1);
}
100% {
    opacity: 0;
    -webkit-transform: scale(0.5);
    transform: scale(0.5);
}
}
@keyframes bounceOut-data-v-5b508c1a {
0% {
    opacity: 1;
    -webkit-transform: scale(1);
    transform: scale(1);
}
100% {
    opacity: 0;
    -webkit-transform: scale(0.5);
    transform: scale(0.5);
}
}
.calendar[data-v-5b508c1a] {
  width: 8.68rem;
  margin: 0 auto;
}
.calendar-weeks span[data-v-5b508c1a] {
  display: inline-block;
  width: 1.24rem;
  height: 0.853333rem;
  line-height: 0.853333rem;
  text-align: center;
  color: #b3b3b3;
}
.calendar-days[data-v-5b508c1a] {
  font-size: 0;
}
.calendar-days.shrink[data-v-5b508c1a] {
  height: 1.706667rem;
  overflow: hidden;
}
.calendar-days .day-item[data-v-5b508c1a] {
  display: inline-block;
  width: 1.24rem;
  height: 0.853333rem;
  overflow: hidden;
  line-height: 0.853333rem;
  text-align: center;
  font-size: 0.4rem;
}
.calendar-days .day-item .day-gray[data-v-5b508c1a] {
  color: #b3b3b3;
}
.calendar-days .day-item .day-icon-reSign[data-v-5b508c1a] {
  font-size: 0.666667rem;
}
.calendar-days .day-item .day-icon-reSign .icon[data-v-5b508c1a] {
  margin-top: 0.093333rem;
}
.calendar-days .day-item .day-icon-has[data-v-5b508c1a] {
  font-size: 0.506667rem;
}
.calendar-days .day-item .day-icon-has .icon[data-v-5b508c1a] {
  margin-top: 0.173333rem;
}
.calendar-days .day-item .day-today[data-v-5b508c1a] {
  color: #fff;
  display: inline-block;
  width: 0.746667rem;
  height: 0.746667rem;
  border-radius: 0.4rem;
  line-height: 0.746667rem;
  margin-top: 0.053333rem;
}
.calendar .change-btn[data-v-5b508c1a] {
  width: 1.333333rem;
  height: 0.666667rem;
  margin: 0 auto;
  text-align: center;
  position: relative;
  overflow: hidden;
}
.calendar .change-btn .btn-bg[data-v-5b508c1a] {
  position: absolute;
  width: 1.333333rem;
  height: 1.333333rem;
  border-radius: 0.666667rem;
  top: 0;
}
.calendar .change-btn .icon[data-v-5b508c1a] {
  margin-top: 0.173333rem;
  font-size: 0.346667rem;
}
.normal-page[data-v-69a75b72],
.normal-page-content[data-v-69a75b72] {
  font-size: 0.32rem;
  width: 100%;
  background-color: #fff;
  position: relative;
}
.opacity-4[data-v-69a75b72] {
  opacity: 0.4;
}
.blur[data-v-69a75b72] {
  -webkit-filter: blur(0.066667rem);
  filter: blur(0.066667rem);
}
.icon[data-v-69a75b72] {
  width: 1em;
  height: 1em;
  vertical-align: -0.15em;
  fill: currentColor;
  overflow: hidden;
}
.scale-enter-active[data-v-69a75b72] {
  -webkit-transition: opacity 0.3s, -webkit-transform 0.3s;
  transition: opacity 0.3s, -webkit-transform 0.3s;
  -o-transition: opacity 0.3s, transform 0.3s;
  transition: opacity 0.3s, transform 0.3s;
  transition: opacity 0.3s, transform 0.3s, -webkit-transform 0.3s;
}
.scale-enter-active > div[data-v-69a75b72]:nth-of-type(1),
.scale-enter-active > div[data-v-69a75b72]:nth-of-type(2),
.scale-enter-active > section[data-v-69a75b72]:first-child {
  -webkit-animation: bounceIn-data-v-69a75b72 0.3s linear alternate both;
  animation: bounceIn-data-v-69a75b72 0.3s linear alternate both;
}
.scale-leave-active[data-v-69a75b72] {
  -webkit-transition: opacity 0.2s, -webkit-transform 0.2s;
  transition: opacity 0.2s, -webkit-transform 0.2s;
  -o-transition: opacity 0.2s, transform 0.2s;
  transition: opacity 0.2s, transform 0.2s;
  transition: opacity 0.2s, transform 0.2s, -webkit-transform 0.2s;
}
.scale-leave-active > div[data-v-69a75b72]:nth-of-type(1),
.scale-leave-active > div[data-v-69a75b72]:nth-of-type(2),
.scale-leave-active > section[data-v-69a75b72]:first-child {
  -webkit-animation: bounceOut-data-v-69a75b72 0.2s linear alternate both;
  animation: bounceOut-data-v-69a75b72 0.2s linear alternate both;
}
.scale-leave-to[data-v-69a75b72] {
  opacity: 0;
}
.scale-leave-to > div[data-v-69a75b72]:nth-of-type(1),
.scale-leave-to > div[data-v-69a75b72]:nth-of-type(2),
.scale-leave-to > section[data-v-69a75b72]:first-child {
  opacity: 0;
}
@-webkit-keyframes bounceIn-data-v-69a75b72 {
0% {
    opacity: 0;
    -webkit-transform: scale(0.5);
    transform: scale(0.5);
}
100% {
    opacity: 1;
    -webkit-transform: scale(1);
    transform: scale(1);
}
}
@keyframes bounceIn-data-v-69a75b72 {
0% {
    opacity: 0;
    -webkit-transform: scale(0.5);
    transform: scale(0.5);
}
100% {
    opacity: 1;
    -webkit-transform: scale(1);
    transform: scale(1);
}
}
@-webkit-keyframes bounceOut-data-v-69a75b72 {
0% {
    opacity: 1;
    -webkit-transform: scale(1);
    transform: scale(1);
}
100% {
    opacity: 0;
    -webkit-transform: scale(0.5);
    transform: scale(0.5);
}
}
@keyframes bounceOut-data-v-69a75b72 {
0% {
    opacity: 1;
    -webkit-transform: scale(1);
    transform: scale(1);
}
100% {
    opacity: 0;
    -webkit-transform: scale(0.5);
    transform: scale(0.5);
}
}
.sign[data-v-69a75b72] {
  width: 100%;
}
.sign-wrap[data-v-69a75b72] {
  width: 9.2rem;
  background: #fffffc;
  -webkit-box-shadow: 0 0.08rem 0.16rem 0 rgba(0, 0, 0, 0.08);
  box-shadow: 0 0.08rem 0.16rem 0 rgba(0, 0, 0, 0.08);
  border-radius: 0.266667rem;
  margin: -0.613333rem auto 0;
  position: relative;
  padding-top: 0.826667rem;
}
.sign-info[data-v-69a75b72] {
  position: absolute;
  width: 5.306667rem;
  height: 2rem;
  top: -1.266667rem;
  right: calc(50% - 2.653333rem);
  bottom: auto;
  left: auto;
  border-radius: 0.213333rem;
  padding: 0.173333rem;
}
.sign-info-main[data-v-69a75b72] {
  width: 100%;
  height: 100%;
  border-radius: 0.266667rem;
  padding: 0.173333rem;
  color: #fff;
  background: #fff;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}
.sign-info-main p[data-v-69a75b72] {
  width: 55%;
  text-align: center;
  font-size: 0.373333rem;
  font-family: PingFangSC;
  font-weight: 600;
  color: #ffffff;
  line-height: 0.533333rem;
}
.sign-info-main h3[data-v-69a75b72] {
  -webkit-box-flex: 1;
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
  font-size: 0.373333rem;
  font-family: PingFangSC;
  font-weight: 600;
  text-align: center;
  padding-top: 0.2rem;
  letter-spacing: 0.026667rem;
}
.sign-info-main h3 span[data-v-69a75b72] {
  font-size: 1.066667rem;
  font-family: PingFangSC;
  font-weight: 600;
  vertical-align: sub;
  line-height: 1.066667rem;
}
.sign-info-main .disable-sign-activity[data-v-69a75b72] {
  font-size: 0.533333rem;
  font-weight: bold;
  text-align: center;
  padding: 0.333333rem 0;
  width: 100%;
  letter-spacing: 0.026667rem;
}
.sign-tip[data-v-69a75b72] {
  font-size: 0.32rem;
  color: #666666;
  line-height: 0.44rem;
  text-align: center;
  margin-bottom: 0.12rem;
}
.sign-line[data-v-69a75b72] {
  width: 7.893333rem;
  height: 0.026667rem;
  margin: 0 auto;
}
.sign-month[data-v-69a75b72] {
  position: relative;
}
.sign-month .current-month[data-v-69a75b72] {
  width: 3.2rem;
  height: 0.706667rem;
  text-align: center;
  line-height: 0.706667rem;
  font-size: 0.426667rem;
  font-weight: 600;
  margin: 0 auto;
  border-radius: 0 0 0.653333rem 0.653333rem;
}
.sign-month .month-change[data-v-69a75b72] {
  position: absolute;
  top: 0.186667rem;
  font-size: 0.346667rem;
}
.sign-month .month-change.pre[data-v-69a75b72] {
  left: 0.613333rem;
}
.sign-month .month-change.next[data-v-69a75b72] {
  right: 0.613333rem;
}
.normal-page[data-v-00f0eab8],
.normal-page-content[data-v-00f0eab8] {
  font-size: 0.32rem;
  width: 100%;
  background-color: #fff;
  position: relative;
}
.opacity-4[data-v-00f0eab8] {
  opacity: 0.4;
}
.blur[data-v-00f0eab8] {
  -webkit-filter: blur(0.066667rem);
  filter: blur(0.066667rem);
}
.icon[data-v-00f0eab8] {
  width: 1em;
  height: 1em;
  vertical-align: -0.15em;
  fill: currentColor;
  overflow: hidden;
}
.scale-enter-active[data-v-00f0eab8] {
  -webkit-transition: opacity 0.3s, -webkit-transform 0.3s;
  transition: opacity 0.3s, -webkit-transform 0.3s;
  -o-transition: opacity 0.3s, transform 0.3s;
  transition: opacity 0.3s, transform 0.3s;
  transition: opacity 0.3s, transform 0.3s, -webkit-transform 0.3s;
}
.scale-enter-active > div[data-v-00f0eab8]:nth-of-type(1),
.scale-enter-active > div[data-v-00f0eab8]:nth-of-type(2),
.scale-enter-active > section[data-v-00f0eab8]:first-child {
  -webkit-animation: bounceIn-data-v-00f0eab8 0.3s linear alternate both;
  animation: bounceIn-data-v-00f0eab8 0.3s linear alternate both;
}
.scale-leave-active[data-v-00f0eab8] {
  -webkit-transition: opacity 0.2s, -webkit-transform 0.2s;
  transition: opacity 0.2s, -webkit-transform 0.2s;
  -o-transition: opacity 0.2s, transform 0.2s;
  transition: opacity 0.2s, transform 0.2s;
  transition: opacity 0.2s, transform 0.2s, -webkit-transform 0.2s;
}
.scale-leave-active > div[data-v-00f0eab8]:nth-of-type(1),
.scale-leave-active > div[data-v-00f0eab8]:nth-of-type(2),
.scale-leave-active > section[data-v-00f0eab8]:first-child {
  -webkit-animation: bounceOut-data-v-00f0eab8 0.2s linear alternate both;
  animation: bounceOut-data-v-00f0eab8 0.2s linear alternate both;
}
.scale-leave-to[data-v-00f0eab8] {
  opacity: 0;
}
.scale-leave-to > div[data-v-00f0eab8]:nth-of-type(1),
.scale-leave-to > div[data-v-00f0eab8]:nth-of-type(2),
.scale-leave-to > section[data-v-00f0eab8]:first-child {
  opacity: 0;
}
@-webkit-keyframes bounceIn-data-v-00f0eab8 {
0% {
    opacity: 0;
    -webkit-transform: scale(0.5);
    transform: scale(0.5);
}
100% {
    opacity: 1;
    -webkit-transform: scale(1);
    transform: scale(1);
}
}
@keyframes bounceIn-data-v-00f0eab8 {
0% {
    opacity: 0;
    -webkit-transform: scale(0.5);
    transform: scale(0.5);
}
100% {
    opacity: 1;
    -webkit-transform: scale(1);
    transform: scale(1);
}
}
@-webkit-keyframes bounceOut-data-v-00f0eab8 {
0% {
    opacity: 1;
    -webkit-transform: scale(1);
    transform: scale(1);
}
100% {
    opacity: 0;
    -webkit-transform: scale(0.5);
    transform: scale(0.5);
}
}
@keyframes bounceOut-data-v-00f0eab8 {
0% {
    opacity: 1;
    -webkit-transform: scale(1);
    transform: scale(1);
}
100% {
    opacity: 0;
    -webkit-transform: scale(0.5);
    transform: scale(0.5);
}
}
.lottery[data-v-00f0eab8] {
  width: 9.2rem;
  margin: 0.4rem auto 0;
  border-radius: 0.186667rem;
  position: relative;
  overflow: hidden;
  -webkit-mask-image: -webkit-radial-gradient(white, black);
}
.lottery .decration-before[data-v-00f0eab8] {
  position: absolute;
  width: 4rem;
  height: 4rem;
  top: -1.6rem;
  right: auto;
  bottom: auto;
  left: -1.6rem;
  border-radius: 2rem;
  z-index: -1;
}
.lottery .decration-after[data-v-00f0eab8] {
  position: absolute;
  width: 4.666667rem;
  height: 4.666667rem;
  top: 3.013333rem;
  right: -1.333333rem;
  bottom: auto;
  left: auto;
  border-radius: 2.333333rem;
  z-index: -1;
}
.lottery-top[data-v-00f0eab8] {
  width: 100%;
  height: 1.946667rem;
  color: red;
  padding: 0 0.266667rem;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}
.lottery-top .icon-tag[data-v-00f0eab8] {
  width: 1.333333rem;
  padding-top: 0.133333rem;
  text-align: center;
  position: relative;
}
.lottery-top .icon-tag span[data-v-00f0eab8] {
  display: block;
  width: 1.333333rem;
  height: 0.4rem;
  font-size: 0.32rem;
  font-family: PingFangSC;
  font-weight: 500;
  color: #ffffff;
  line-height: 0.4rem;
  letter-spacing: 0.053333rem;
}
.lottery-top .icon-tag .icon[data-v-00f0eab8] {
  font-size: 1.333333rem;
  position: absolute;
  top: 0;
  left: 0;
  z-index: -1;
}
.lottery-top .lottery-info[data-v-00f0eab8] {
  margin-top: 0.4rem;
  margin-left: 0.266667rem;
  -webkit-box-flex: 1;
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
}
.lottery-top .lottery-info p[data-v-00f0eab8] {
  height: 0.44rem;
  font-size: 0.32rem;
  font-family: PingFangSC;
  font-weight: 500;
  line-height: 0.44rem;
  margin-bottom: 0.08rem;
}
.lottery-top .start-btn[data-v-00f0eab8] {
  margin-top: 0.4rem;
  background: #fff;
  width: 2.773333rem;
  height: 1.146667rem;
  border-radius: 0.573333rem;
  line-height: 1.146667rem;
  text-align: center;
  color: #fff;
  font-size: 0.426667rem;
  font-family: PingFangSC;
  font-weight: 600;
}
.lottery-prize[data-v-00f0eab8] {
  height: 2.986667rem;
  padding-top: 0.16rem;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  overflow-x: scroll;
  -webkit-justify-content: space-around;
  -ms-flex-pack: distribute;
  justify-content: space-around;
}
.lottery-prize.scroll[data-v-00f0eab8] {
  overflow-x: scroll;
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
  -ms-flex-pack: start;
  justify-content: flex-start;
}
.lottery-prize.scroll .lottery-prize-item[data-v-00f0eab8] {
  margin-right: 0.24rem;
}
.lottery-prize-item[data-v-00f0eab8] {
  display: inline-block;
  text-align: center;
}
.lottery-prize-item .img-box[data-v-00f0eab8] {
  margin: 0 auto;
  width: 1.946667rem;
  height: 1.946667rem;
  border-radius: 0.106667rem;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -webkit-align-items: center;
  -ms-flex-align: center;
  align-items: center;
}
.lottery-prize-item .img-box img[data-v-00f0eab8] {
  max-width: 1.946667rem;
  max-height: 1.946667rem;
}
.lottery-prize-item p[data-v-00f0eab8] {
  height: 0.44rem;
  font-size: 0.32rem;
  font-family: PingFangSC;
  font-weight: 400;
  color: #666666;
  line-height: 0.44rem;
  margin-top: 0.106667rem;
}
.normal-page[data-v-6171275f],
.normal-page-content[data-v-6171275f] {
  font-size: 0.32rem;
  width: 100%;
  background-color: #fff;
  position: relative;
}
.opacity-4[data-v-6171275f] {
  opacity: 0.4;
}
.blur[data-v-6171275f] {
  -webkit-filter: blur(0.066667rem);
  filter: blur(0.066667rem);
}
.icon[data-v-6171275f] {
  width: 1em;
  height: 1em;
  vertical-align: -0.15em;
  fill: currentColor;
  overflow: hidden;
}
.scale-enter-active[data-v-6171275f] {
  -webkit-transition: opacity 0.3s, -webkit-transform 0.3s;
  transition: opacity 0.3s, -webkit-transform 0.3s;
  -o-transition: opacity 0.3s, transform 0.3s;
  transition: opacity 0.3s, transform 0.3s;
  transition: opacity 0.3s, transform 0.3s, -webkit-transform 0.3s;
}
.scale-enter-active > div[data-v-6171275f]:nth-of-type(1),
.scale-enter-active > div[data-v-6171275f]:nth-of-type(2),
.scale-enter-active > section[data-v-6171275f]:first-child {
  -webkit-animation: bounceIn-data-v-6171275f 0.3s linear alternate both;
  animation: bounceIn-data-v-6171275f 0.3s linear alternate both;
}
.scale-leave-active[data-v-6171275f] {
  -webkit-transition: opacity 0.2s, -webkit-transform 0.2s;
  transition: opacity 0.2s, -webkit-transform 0.2s;
  -o-transition: opacity 0.2s, transform 0.2s;
  transition: opacity 0.2s, transform 0.2s;
  transition: opacity 0.2s, transform 0.2s, -webkit-transform 0.2s;
}
.scale-leave-active > div[data-v-6171275f]:nth-of-type(1),
.scale-leave-active > div[data-v-6171275f]:nth-of-type(2),
.scale-leave-active > section[data-v-6171275f]:first-child {
  -webkit-animation: bounceOut-data-v-6171275f 0.2s linear alternate both;
  animation: bounceOut-data-v-6171275f 0.2s linear alternate both;
}
.scale-leave-to[data-v-6171275f] {
  opacity: 0;
}
.scale-leave-to > div[data-v-6171275f]:nth-of-type(1),
.scale-leave-to > div[data-v-6171275f]:nth-of-type(2),
.scale-leave-to > section[data-v-6171275f]:first-child {
  opacity: 0;
}
@-webkit-keyframes bounceIn-data-v-6171275f {
0% {
    opacity: 0;
    -webkit-transform: scale(0.5);
    transform: scale(0.5);
}
100% {
    opacity: 1;
    -webkit-transform: scale(1);
    transform: scale(1);
}
}
@keyframes bounceIn-data-v-6171275f {
0% {
    opacity: 0;
    -webkit-transform: scale(0.5);
    transform: scale(0.5);
}
100% {
    opacity: 1;
    -webkit-transform: scale(1);
    transform: scale(1);
}
}
@-webkit-keyframes bounceOut-data-v-6171275f {
0% {
    opacity: 1;
    -webkit-transform: scale(1);
    transform: scale(1);
}
100% {
    opacity: 0;
    -webkit-transform: scale(0.5);
    transform: scale(0.5);
}
}
@keyframes bounceOut-data-v-6171275f {
0% {
    opacity: 1;
    -webkit-transform: scale(1);
    transform: scale(1);
}
100% {
    opacity: 0;
    -webkit-transform: scale(0.5);
    transform: scale(0.5);
}
}
.operate[data-v-6171275f] {
  width: 100%;
  font-size: 0;
}
.operate .solid-line[data-v-6171275f] {
  width: 100%;
  height: 0.266667rem;
  background: #f3f3f3;
}
.operate-list[data-v-6171275f] {
  width: 9.2rem;
  padding: 0.4rem 0 0;
  margin: 0 auto;
}
.operate-list .operate-item[data-v-6171275f] {
  display: block;
  margin-bottom: 0.4rem;
}
.operate-list .operate-item img[data-v-6171275f] {
  width: 100%;
  height: 2.666667rem;
}
.normal-page[data-v-1387ec93],
.normal-page-content[data-v-1387ec93] {
  font-size: 0.32rem;
  width: 100%;
  background-color: #fff;
  position: relative;
}
.opacity-4[data-v-1387ec93] {
  opacity: 0.4;
}
.blur[data-v-1387ec93] {
  -webkit-filter: blur(0.066667rem);
  filter: blur(0.066667rem);
}
.icon[data-v-1387ec93] {
  width: 1em;
  height: 1em;
  vertical-align: -0.15em;
  fill: currentColor;
  overflow: hidden;
}
.scale-enter-active[data-v-1387ec93] {
  -webkit-transition: opacity 0.3s, -webkit-transform 0.3s;
  transition: opacity 0.3s, -webkit-transform 0.3s;
  -o-transition: opacity 0.3s, transform 0.3s;
  transition: opacity 0.3s, transform 0.3s;
  transition: opacity 0.3s, transform 0.3s, -webkit-transform 0.3s;
}
.scale-enter-active > div[data-v-1387ec93]:nth-of-type(1),
.scale-enter-active > div[data-v-1387ec93]:nth-of-type(2),
.scale-enter-active > section[data-v-1387ec93]:first-child {
  -webkit-animation: bounceIn-data-v-1387ec93 0.3s linear alternate both;
  animation: bounceIn-data-v-1387ec93 0.3s linear alternate both;
}
.scale-leave-active[data-v-1387ec93] {
  -webkit-transition: opacity 0.2s, -webkit-transform 0.2s;
  transition: opacity 0.2s, -webkit-transform 0.2s;
  -o-transition: opacity 0.2s, transform 0.2s;
  transition: opacity 0.2s, transform 0.2s;
  transition: opacity 0.2s, transform 0.2s, -webkit-transform 0.2s;
}
.scale-leave-active > div[data-v-1387ec93]:nth-of-type(1),
.scale-leave-active > div[data-v-1387ec93]:nth-of-type(2),
.scale-leave-active > section[data-v-1387ec93]:first-child {
  -webkit-animation: bounceOut-data-v-1387ec93 0.2s linear alternate both;
  animation: bounceOut-data-v-1387ec93 0.2s linear alternate both;
}
.scale-leave-to[data-v-1387ec93] {
  opacity: 0;
}
.scale-leave-to > div[data-v-1387ec93]:nth-of-type(1),
.scale-leave-to > div[data-v-1387ec93]:nth-of-type(2),
.scale-leave-to > section[data-v-1387ec93]:first-child {
  opacity: 0;
}
@-webkit-keyframes bounceIn-data-v-1387ec93 {
0% {
    opacity: 0;
    -webkit-transform: scale(0.5);
    transform: scale(0.5);
}
100% {
    opacity: 1;
    -webkit-transform: scale(1);
    transform: scale(1);
}
}
@keyframes bounceIn-data-v-1387ec93 {
0% {
    opacity: 0;
    -webkit-transform: scale(0.5);
    transform: scale(0.5);
}
100% {
    opacity: 1;
    -webkit-transform: scale(1);
    transform: scale(1);
}
}
@-webkit-keyframes bounceOut-data-v-1387ec93 {
0% {
    opacity: 1;
    -webkit-transform: scale(1);
    transform: scale(1);
}
100% {
    opacity: 0;
    -webkit-transform: scale(0.5);
    transform: scale(0.5);
}
}
@keyframes bounceOut-data-v-1387ec93 {
0% {
    opacity: 1;
    -webkit-transform: scale(1);
    transform: scale(1);
}
100% {
    opacity: 0;
    -webkit-transform: scale(0.5);
    transform: scale(0.5);
}
}
.sign-toast[data-v-1387ec93] {
  position: fixed;
  width: 4.746667rem;
  padding: 0.266667rem 0;
  text-align: center;
  top: 30%;
  right: calc(50% - 2.373333rem);
  background-color: rgba(0, 0, 0, 0.6);
  color: #fff;
  z-index: 1002;
  font-size: 0.4rem;
  font-family: PingFangSC;
  font-weight: 400;
  line-height: 0.56rem;
  border-radius: 0.32rem;
}
.sign-toast .header-img[data-v-1387ec93] {
  width: 1.44rem;
  height: 0.773333rem;
  background: url(//yun.duiba.com.cn/h5/sign_custom/skins/normal-calendar-sign_190826/image/signSuc-title.cff7ed78.png);
  background-size: 100% 100%;
  background-repeat: no-repeat;
  margin: 0 auto 0.2rem;
}
[duiba-webp='true'] .sign-toast .header-img[data-v-1387ec93] {
  background-image: url(//yun.duiba.com.cn/h5/sign_custom/skins/normal-calendar-sign_190826/image/signSuc-title.cff7ed78.png?x-oss-process=image/format,webp);
  background-size: 100% 100%;
  background-repeat: no-repeat;
}
.sign-toast .value-color[data-v-1387ec93] {
  color: #F63D3D;
}
/*大转盘*/
.drawPrize-wrap[data-v-2f13ac30] {
  position: fixed;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  z-index: 99;
  background: rgba(0, 0, 0, 0.7);
}
.drawPrize-wrap .close[data-v-2f13ac30] {
  position: absolute;
  right: 0.8rem;
  top: -2.88rem;
  display: block;
  margin: 0 auto;
  width: 0.773333rem;
  height: 0.773333rem;
  background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADoAAAA6CAMAAADWZboaAAAAV1BMVEUAAAD///////////////////////////////////////////////////////////////////////////////////////////////////////////////+ORg7oAAAAHHRSTlMA+AyMoMXvIkAGTXZ8txHiXtwy1s+nZFZQGOiucfrluAAAAdVJREFUSMell21zgyAQhO8UEQUVjUnTdP//7+yUTmpVLtG5/eKM+gje8rJQVo3zphotsx0r411DB1XEjrESd7E4ANYmcbMJsXWujcHMiTb1O/CaXrv39E/9PX3u+gpuAgPDrcj8xG0AOIg//VkB1guPG2+B6pOymgZw2ZOovmQME2X0wbCOXspZ8Aft5IFL/bb8F8Dv2gS64oDnHbBpd2J0dEgdeFrVdsClOIYWFwz/6txUsDUdVG1RLQYGsKPDcozw9x1GSSdUgp99vML2Z9De4kpJNeDplDzw26zB0JxDmwEmVZtxo5O6gX+8jOmyUr91atqay4hpeJh9GcrVi9VuqJmfOw3jvjUdQLkiMdJad3BDDuj3xiV2Ibnddgxw5DETCeyOXDTDk4EhiZXJhFUIJLEySQEVjYgksiJJESNZtCSyD4mkFpYYjkRWJMmBX6DFA8AXiWiuw4srqVZih0dEkeQvkY0Yc+Ysfi7+5swxMDJJEpswj1kmZXaGT8NfImW2B1xu0tGTXNiQmXS5qT49yYV95KY6RdiC1urGtdfhEfYLjGJZUyymiiVcsXEotivFJqnYmhWBQBFDFOFHEbkUQU8RLxWhVhGlFQFecWzQHFYUR6RvDpEu+qF6kaEAAAAASUVORK5CYII=) no-repeat top center;
  background-size: 100% 100%;
}
.drawPrize-bd[data-v-2f13ac30] {
  position: absolute;
  top: -5%;
  left: 0;
  bottom: 0;
  right: 0;
  margin: auto;
  width: 10rem;
  height: 10.533333rem;
}
.drawPrize-close[data-v-2f13ac30] {
  width: 0.906667rem;
  height: 0.906667rem;
  position: absolute;
  right: 0.666667rem;
  top: 1rem;
  z-index: 999;
  background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADoAAAA6CAMAAADWZboaAAAAV1BMVEUAAAD///////////////////////////////////////////////////////////////////////////////////////////////////////////////+ORg7oAAAAHHRSTlMA+AyMoMXvIkAGTXZ8txHiXtwy1s+nZFZQGOiucfrluAAAAdVJREFUSMell21zgyAQhO8UEQUVjUnTdP//7+yUTmpVLtG5/eKM+gje8rJQVo3zphotsx0r411DB1XEjrESd7E4ANYmcbMJsXWujcHMiTb1O/CaXrv39E/9PX3u+gpuAgPDrcj8xG0AOIg//VkB1guPG2+B6pOymgZw2ZOovmQME2X0wbCOXspZ8Aft5IFL/bb8F8Dv2gS64oDnHbBpd2J0dEgdeFrVdsClOIYWFwz/6txUsDUdVG1RLQYGsKPDcozw9x1GSSdUgp99vML2Z9De4kpJNeDplDzw26zB0JxDmwEmVZtxo5O6gX+8jOmyUr91atqay4hpeJh9GcrVi9VuqJmfOw3jvjUdQLkiMdJad3BDDuj3xiV2Ibnddgxw5DETCeyOXDTDk4EhiZXJhFUIJLEySQEVjYgksiJJESNZtCSyD4mkFpYYjkRWJMmBX6DFA8AXiWiuw4srqVZih0dEkeQvkY0Yc+Ysfi7+5swxMDJJEpswj1kmZXaGT8NfImW2B1xu0tGTXNiQmXS5qT49yYV95KY6RdiC1urGtdfhEfYLjGJZUyymiiVcsXEotivFJqnYmhWBQBFDFOFHEbkUQU8RLxWhVhGlFQFecWzQHFYUR6RvDpEu+qF6kaEAAAAASUVORK5CYII=) no-repeat top center;
  background-size: 100% 100%;
}
.lottery-count[data-v-2f13ac30] {
  margin-bottom: 0.733333rem;
  font-family: PingFangSC-Regular;
  font-size: 0.453333rem;
  color: #ffffff;
  text-align: center;
  line-height: 0.64rem;
}
.lottery-count span[data-v-2f13ac30] {
  padding: 0 0.16rem;
  font-family: PingFangSC-Regular;
  font-size: 0.453333rem;
  color: #f63d3d;
  text-align: center;
  line-height: 0.64rem;
}
.turntable-wrap[data-v-2f13ac30] {
  position: relative;
  margin: 0 auto;
}
.turntable-bd[data-v-2f13ac30] {
  width: 8.533333rem;
  height: 8.533333rem;
  margin: 0 auto;
  background: url(//yun.duiba.com.cn/h5/sign_custom/skins/normal-calendar-sign_190826/image/zhuanpan-bd.1a26ba28.png) no-repeat top center;
  background-size: 100% 100%;
}
#turntable[data-v-2f13ac30] {
  position: absolute;
  top: 0.493333rem;
  left: 1.24rem;
  width: 7.52rem;
  height: 7.52rem;
  overflow: hidden;
  border-radius: 50%;
  background: url(//yun.duiba.com.cn/h5/sign_custom/skins/normal-calendar-sign_190826/image/zhuanpan.c91ca030.png) no-repeat top center;
  background-size: 100% 100%;
  -webkit-mask-image: -webkit-radial-gradient(white, black);
}
#turntable .prize[data-v-2f13ac30] {
  position: absolute;
  top: 0.293333rem;
  left: 1.493333rem;
  width: 2.266667rem;
  height: 3.466667rem;
  -webkit-transform-origin: right bottom;
  -ms-transform-origin: right bottom;
  transform-origin: right bottom;
}
#turntable .prize[data-v-2f13ac30]:nth-child(1) {
  -webkit-transform: rotate(45deg) skewY(45deg);
  -ms-transform: rotate(45deg) skewY(45deg);
  transform: rotate(45deg) skewY(45deg);
}
#turntable .prize[data-v-2f13ac30]:nth-child(2) {
  -webkit-transform: rotate(90deg) skewY(45deg);
  -ms-transform: rotate(90deg) skewY(45deg);
  transform: rotate(90deg) skewY(45deg);
}
#turntable .prize[data-v-2f13ac30]:nth-child(3) {
  -webkit-transform: rotate(135deg) skewY(45deg);
  -ms-transform: rotate(135deg) skewY(45deg);
  transform: rotate(135deg) skewY(45deg);
}
#turntable .prize[data-v-2f13ac30]:nth-child(4) {
  -webkit-transform: rotate(180deg) skewY(45deg);
  -ms-transform: rotate(180deg) skewY(45deg);
  transform: rotate(180deg) skewY(45deg);
}
#turntable .prize[data-v-2f13ac30]:nth-child(5) {
  -webkit-transform: rotate(225deg) skewY(45deg);
  -ms-transform: rotate(225deg) skewY(45deg);
  transform: rotate(225deg) skewY(45deg);
}
#turntable .prize[data-v-2f13ac30]:nth-child(6) {
  -webkit-transform: rotate(270deg) skewY(45deg);
  -ms-transform: rotate(270deg) skewY(45deg);
  transform: rotate(270deg) skewY(45deg);
}
#turntable .prize[data-v-2f13ac30]:nth-child(7) {
  -webkit-transform: rotate(315deg) skewY(45deg);
  -ms-transform: rotate(315deg) skewY(45deg);
  transform: rotate(315deg) skewY(45deg);
}
#turntable .prize[data-v-2f13ac30]:nth-child(8) {
  -webkit-transform: rotate(360deg) skewY(45deg);
  -ms-transform: rotate(360deg) skewY(45deg);
  transform: rotate(360deg) skewY(45deg);
}
#turntable .prize .prize-dialog[data-v-2f13ac30] {
  position: absolute;
  right: 0.479855rem;
  bottom: 0.96rem;
  width: 1.266667rem;
  height: 1.266667rem;
  -webkit-transform: skewY(-45deg) rotate(-22.5deg);
  -ms-transform: skewY(-45deg) rotate(-22.5deg);
  transform: skewY(-45deg) rotate(-22.5deg);
  text-align: center;
}
#turntable .prize .prize-dialog img[data-v-2f13ac30] {
  width: 100%;
}
#turntable .prize .prize-dialog p[data-v-2f13ac30] {
  line-height: 0.4rem;
  font-family: PingFangSC-Regular;
  font-size: 0.266667rem;
  color: #3d3d3d;
  margin-top: 0.106667rem;
  margin-bottom: 0.066667rem;
  text-align: center;
  overflow: hidden;
}
#start[data-v-2f13ac30] {
  position: absolute;
  width: 2.053333rem;
  height: 2.586667rem;
  left: 3.933333rem;
  top: 2.733333rem;
  background: url(//yun.duiba.com.cn/h5/sign_custom/skins/normal-calendar-sign_190826/image/start-btn.ee37b424.png) no-repeat top center;
  background-size: 100% 100%;
  z-index: 980;
  -webkit-transform: translateZ(1.32rem);
  transform: translateZ(1.32rem);
}
#start[data-v-2f13ac30]::after {
  position: absolute;
  content: '';
  width: 1.52rem;
  height: 1.626667rem;
  left: 2.133333rem;
  top: 2.666667rem;
  background-size: 100%;
  background-repeat: no-repeat;
  -webkit-animation: hands-data-v-2f13ac30 0.5s infinite alternate linear;
  animation: hands-data-v-2f13ac30 0.5s infinite alternate linear;
}
@-webkit-keyframes hands-data-v-2f13ac30 {
from {
    -webkit-transform: translate(0.133333rem, 0.133333rem);
    transform: translate(0.133333rem, 0.133333rem);
}
}
@keyframes hands-data-v-2f13ac30 {
from {
    -webkit-transform: translate(0.133333rem, 0.133333rem);
    transform: translate(0.133333rem, 0.133333rem);
}
}
.scratchCard-dialog[data-v-4e5e9886] {
  position: fixed;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  z-index: 99;
  background: rgba(0, 0, 0, 0.7);
}
.scratchCard-dialog .scratchCard-content[data-v-4e5e9886] {
  margin-left: -5rem;
  -webkit-animation: slideInDown-data-v-4e5e9886 0.6s linear;
  animation: slideInDown-data-v-4e5e9886 0.6s linear;
  position: fixed;
  left: 50%;
  bottom: 0;
  text-align: center;
}
.scratchCard-dialog .scratchCard-content .scratchCard-wrap[data-v-4e5e9886] {
  width: 10rem;
  height: 5.733333rem;
  background: url(//yun.duiba.com.cn/h5/sign_custom/skins/normal-calendar-sign_190826/image/scratchCardModal.a122649e.png);
  background-size: 100% 100%;
}
.scratchCard-dialog .scratchCard-content .close-btn[data-v-4e5e9886] {
  position: absolute;
  right: 0.293333rem;
  top: 0.373333rem;
  width: 0.533333rem;
  height: 0.533333rem;
  background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACgAAAAoCAYAAAH7+Yj7AAAAAXNSR0IArs4c6QAAAe5JREFUWAntWNttwzAMjAt0g47SZfLT/CTof5o1skazQZEhupN75/IUWbEs+oEiKBSAoB6845mRZdmbzeRf27bfNAKfJqMfFKALkry0r/HgJwcUAYH6wRoQ/kGTrPRCGk3QR4Hbpmle47m7NpkiQJjvrSoGGROaNxkhmg0GxQPoH2GHeKy2rVDZKlqFWEzY1V0wEhoorBKCbaz3z8SkvZUTT6hNYrS36pdWVG/hCJR4kX1y3BIkIY5u7tIwzoXH+p0cNL8hCP4qBSNmD3srxdX5WoE5FcDausKy9yo5Md/d525+EuZIRUbvJjQVd6SzyZSZKmmWYHA7U6y8Z/uK63nB9nUWeMh7tq+LgCUyxo0SWuG5H2ovjNUqj89Hf8BRiLimGnN5AE8GDmQCTiYF4JAjm0UKsh1sL3DOI6a4q+ewdbxWoFbgH1TANgs+Whe/jJADRq6dpzSju39E8Iz2C+wdxHwITnscA0gMseQwLnKu+1MSJjIrCp2DWazak9QT4xFSPIKMkVAE5vVCxFCdLnpjnpNGLs8igSIdENqJXSJM3IsFZsSJv3joU+DqfmCNhdMU5vSa6r6ZVhM4JixN8qdCkUznTlUlVCwVlvYHhPo/AKRkQ30k6F7Q4CnOLSzlSoT6v/KkRLVfK1ArcKvAD5niJpwrsWxQAAAAAElFTkSuQmCC);
  background-size: 100% 100%;
}
.scratchCard-dialog .scratchCard-content .scratchCard-bd[data-v-4e5e9886] {
  position: relative;
  width: 100%;
  border-top: 0.013333rem solid transparent;
  background-size: contain;
  background-repeat: no-repeat;
  z-index: 20;
  padding-bottom: 0.8rem;
}
.scratchCard-dialog .scratchCard-content .scratchCard-bd .core[data-v-4e5e9886] {
  position: relative;
  overflow: hidden;
  width: 9.2rem;
  height: 4rem;
  margin: 0 auto;
  padding: 0.533333rem 0;
  background-repeat: no-repeat;
  background-size: 100% 100%;
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAArIAAAEsBAMAAADN000dAAAAJFBMVEUAAAD///////////////////////////////////////////+0CY3pAAAAC3RSTlMANOvazYyDYVoUDV/ugR0AAAQNSURBVHja7NE9SsZAGEXhWAhqZf9tQHAFYm9jY28ruANtxRUIbsDeyr/GbM4wHEiIjGkcmAvnVLd4YeCZYZz6Hkqv6/1S5tG42C1O5l056e3RrZNSkX0bSrv1Pi5zb1zsFifzrpz09ujWySz7NZSu1/uqzP1xsVuczLty0tuj2yfIgo01e/6P3XK3OWHXTrp7dOtklj0bpp7H9f54mubh+XK3OWHXTrp7dOtkln1/GIaD0/HXPpkuH8tiNzphV076e3TjBNnS5+3dZRmrfXF/w2I3OWFXTnp89O8TZO1/U3aVsjEpS8rGpCwpG5OypGxMypKyMSlLysakLCkbk7KkbEzKkrIxKUvKxqQsKRuTsqRsTMqSsjEpS8rGpCwpG5OypGxMypKyMSlLysakLCkbk7KkbEzKkrIxKUvKxqQsKRuTsqRsTMqSsjEpS8rGpCwpG5OypGxMypKyMSlLysakLCkbk7KkbEzKkrIxKUvKxqQsKRuTsqRsTMqSsjEpS8rGpCwpG5OypGxMyv6wQ8c0AAAADIP8u94zE01AAmc2w+yZzTB7ZjPMntkMs2c2w+yZzTB7ZjPMntkMs2c2w+yZzTB7ZjPMntkMs2c2w+yZzTB7ZjPMntkMs2c2w+yZzTB7ZjPMntkMs2c2w+yZzTB7ZjPMntkMs2c2w+yZzTB7ZjPMntkMs2c2w+yZzTB7ZjPMntkMs2c2w+yZzTB7ZjPMntkMs2c2w+yZzTB7ZjPMntkMs2c2w+yZzTB7ZjPMntkMs2uP7lEyiMEoCsdCUCt7NyC4ArG3sbG3FdyBtuIKBDdgb+Vf42zOz+HADCMhVSAXzqlu8ULgCSkbk7KkbEzKkrIxKUvKxqQsKRuTsqRsTMqSsjEpS8rGpCwpG5OypGxMypKyMSlLysakLCkbk7KkbEzKkrIxKUvKxqQsKRuTsqRsTMqSsjEpS8rGpCwpG5OypGxMypKyMSlLysakLCkbk7KkbEzKkrIxKUvKxqQsKRuTsqRsTMqSsjEpS8rGpCwpG5OypGxMypKyMSlLysakLCkbk7KkbEzKkrIxKUvKxqQsKRuTsqRsTMqSsjEpS8rGpCwpG5OypGxMypKyMSlLysakLCkbk7KkbEzKkrIxKUvKxqQsKRuTsqRsTMqSsjEpS8rGpCwpG5OypGxMylIv2a+7+6uJ1vvy4ZbF7nLCrpyM+Gj7BNmPx1IOzqZ/+7SU8sQlu8sJu3Iy3qOtk0X2vOx6mbb783k3Dy/Wu88Ju3Yy3KOtk0X2uOzam7b7vfx1st59Tti1k+EebZ8g+13mbrb7ep7702r3OFl25WS0R5snyGIN9rL5Dv6D3eNk2ZWT0R5tnSyyP2Xubbtf53k0rXaPk2VXTkZ7tHUy9wu623MWJgm2rAAAAABJRU5ErkJggg==);
}
.scratchCard-dialog .scratchCard-content .scratchCard-bd .result-show[data-v-4e5e9886] {
  z-index: 1;
  text-align: center;
  background-color: #fff8da;
}
.scratchCard-dialog .scratchCard-content .scratchCard-bd .result-show .result-show-dialog[data-v-4e5e9886] {
  font-size: 0.32rem;
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  color: #4e4001;
}
.scratchCard-dialog .scratchCard-content .scratchCard-bd .result-show .result-show-dialog[data-v-4e5e9886] img {
  width: 2.933333rem;
  max-height: 1.6rem;
}
.scratchCard-dialog .scratchCard-content .scratchCard-bd .result-show .result-show-dialog[data-v-4e5e9886] p {
  margin-top: 0.133333rem;
}
.scratchCard-dialog .scratchCard-content .scratchCard-bd .canvascard[data-v-4e5e9886] {
  z-index: 2;
}
.scratchCard-dialog .scratchCard-content .scratchCard-bd .canvascard[data-v-4e5e9886],
.scratchCard-dialog .scratchCard-content .scratchCard-bd .error-show[data-v-4e5e9886],
.scratchCard-dialog .scratchCard-content .scratchCard-bd .result-show[data-v-4e5e9886],
.scratchCard-dialog .scratchCard-content .scratchCard-bd .start-dialog[data-v-4e5e9886] {
  position: absolute;
  top: 0.333333rem;
  left: 0.333333rem;
  width: 8.533333rem;
  height: 3.333333rem;
}
.scratchCard-dialog .scratchCard-content .scratchCard-bd .start-tip[data-v-4e5e9886] {
  position: absolute;
  z-index: 2;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  pointer-events: none;
}
.scratchCard-dialog .scratchCard-content .scratchCard-bd .start-tip .radius-tip[data-v-4e5e9886] {
  width: 1.066667rem;
  height: 1.066667rem;
  margin: 0 auto;
  margin-bottom: 0.266667rem;
  background-image: url('data:image/png;base64,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');
  background-repeat: no-repeat;
  background-size: 100% 100%;
}
.scratchCard-dialog .scratchCard-content .scratchCard-bd .start-tip .rect-tip[data-v-4e5e9886] {
  width: 2.786667rem;
  height: 0.333333rem;
  background-image: url('data:image/png;base64,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');
  background-repeat: no-repeat;
  background-size: 100% 100%;
  position: relative;
}
.scratchCard-dialog .scratchCard-content .scratchCard-bd .start[data-v-4e5e9886] {
  position: absolute;
  z-index: 3;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}
.scratchCard-dialog .scratchCard-content .scratchCard-bd .start-dialog[data-v-4e5e9886] {
  position: absolute;
  text-align: center;
  background-color: #fff3ca;
  background-image: url(//yun.duiba.com.cn/h5/sign_custom/skins/normal-calendar-sign_190826/image/start-dialog-bg.a20270d9.png);
  background-repeat: no-repeat;
  background-size: 100% 100%;
}
.scratchCard-dialog .scratchCard-content .scratchCard-bd .start-dialog .start-title[data-v-4e5e9886] {
  font-size: 0.426667rem;
  font-weight: bold;
  margin: 0.666667rem 0 0.133333rem;
  line-height: 2;
  color: #8b7313;
}
.scratchCard-dialog .scratchCard-content .scratchCard-bd .loading[data-v-4e5e9886] {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0.8rem;
  height: 0.76rem;
  -webkit-transform: translate(-50%, -50%) rotate(0deg);
  -ms-transform: translate(-50%, -50%) rotate(0deg);
  transform: translate(-50%, -50%) rotate(0deg);
  -webkit-animation: loading-data-v-4e5e9886 1s linear infinite normal;
  animation: loading-data-v-4e5e9886 1s linear infinite normal;
  background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADwAAAA5CAMAAABd77jzAAAAnFBMVEUAAAD///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////+TINBkAAAAM3RSTlMAMx8YUWMTDQjm3bSVLiUbBQL768yO9aJ6bkRAw7lyXlcp7+rh1dC+qJqHg3VpOa9KiX7DqslnAAAB0klEQVRIx+XV2ZKiMBiG4Y9FZBFZRBC13bXdu2f++7+3MQkpe7qQSnLY/RylKr5qIAG0SqYehPi4K67QMchpM+GjOzEfOi0v6sfIHxHXU24TEeQ+MCXhoBy7JNjPYYiHZXWceIq/XPqAQ8IfAG9sMIrRbl1FCz4Y8+DGhgviSR84ERekra3F5iJRl7StwdUjoveE/4eua7f/MuetfTT61hoPMxkP2lqfuAjtYhlf0CILiJnghZCEIdqs2NTOfxVb1Pnly31w7LiP8TyncAn0b4PLumU+RafEBvDJd+wZJiwSxs/l3mzV+J0aTZFFRLmr1jr0bbOcifGU4iFJC3AHYu5KcRrIeIov58CBkloe0qzZvYXOsyIS58yG1LveoawuynDchylWmrNO++I8NGsvxLkm7ZIasX6bljJ+Awbh3NaJY5K2/PUQ6Fy/GUkb5M+9pns2QvEsT8DonsoJnILKT+hwNqKd81vuQI89Z21lvMVW7hC/WK9nnGYfRFFqUsqdPjOMHfHmNORuAxfqvP+Pka+1YsfFz5HBnLs7GN8wnz9MDchX76BzVbMEL9nVuHPVfVrAnOWpfcyGsSvllhwPdc9bRLSSYyJLL/ZOfyFVxesl/ANGGFK6bCVA/wAAAABJRU5ErkJggg==) no-repeat;
  background-size: 100% 100%;
}
.scratchCard-dialog .scratchCard-content .scratchCard-bd .prize-tip[data-v-4e5e9886] {
  color: #666;
  margin-top: 0.826667rem;
  font-family: PingFangSC-Regular;
  font-size: 0.373333rem;
  text-align: center;
}
.scratchCard-dialog .scratchCard-content .scratchCard-bd .prize-tip span[data-v-4e5e9886] {
  color: #ff8200;
  padding: 0 0.066667rem;
}
.scratchCard-dialog .scratchCard-content .scratchCard-bd .start-btn[data-v-4e5e9886] {
  width: 4.533333rem;
  height: 0.986667rem;
  line-height: 0.986667rem;
  margin: 0.24rem auto;
  background: #ff8200;
  border-radius: 1.333333rem;
  font-family: PingFangSC-Regular;
  font-size: 0.426667rem;
  color: #ffffff;
  text-align: center;
}
.scratchCard-dialog .scratchCard-content .scratchCard-bd .start-btn.disabled[data-v-4e5e9886] {
  background-image: none;
  border-radius: 0.986667rem;
  background-color: #bebebe;
  -webkit-box-shadow: none;
  box-shadow: none;
}
.scratchCard-dialog .scratchCard-content .scratchCard-bd .status[data-v-4e5e9886] {
  text-align: center;
  color: #fff;
}
.scratchCard-dialog .scratchCard-content .scratchCard-bd .error-show[data-v-4e5e9886] {
  font-size: 0.48rem;
  z-index: 3;
  text-align: center;
  color: #fff;
}
.scratchCard-dialog .scratchCard-content .scratchCard-bd .error-show .dialog[data-v-4e5e9886] {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
}
.scratchCard-dialog .scratchCard-content .scratchCard-bd .error-show .dialog p[data-v-4e5e9886] {
  white-space: nowrap;
}
.scratchCard-dialog .scratchCard-content .scratchCard-bd .sorry[data-v-4e5e9886] {
  font-size: 0.4rem;
  margin-bottom: 0.4rem;
  white-space: nowrap;
  color: #4e4001;
}
.scratchCard-dialog .scratchCard-content .scratchCard-bd .m-footer[data-v-4e5e9886] {
  position: absolute;
  width: 100%;
  color: #000 !important;
  font-size: 0.32rem;
  text-align: center;
  line-height: 1.1;
  margin: 0.4rem 0 0 0;
}
.scratchCard-dialog .scratchCard-content .scratchCard-bd .m-footer a[data-v-4e5e9886] {
  display: inline-block;
  padding: 0 0.266667rem;
}
.scratchCard-dialog .scratchCard-content .scratchCard-bd .m-footer a[data-v-4e5e9886]:first-child {
  border-right: 0.026667rem solid #999;
  margin-right: 0.2rem;
}
.scratchCard-dialog .scratchCard-content .scratchCard-bd .m-footer .record[data-v-4e5e9886],
.scratchCard-dialog .scratchCard-content .scratchCard-bd .m-footer .rule[data-v-4e5e9886] {
  display: inline-block;
  margin-top: 0.133333rem;
  background: no-repeat;
  background-size: contain;
  padding-left: 0.533333rem;
}
.scratchCard-dialog .scratchCard-content .scratchCard-bd .m-footer .record[data-v-4e5e9886] {
  background-image: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAMAAADXqc3KAAAAWlBMVEUAAABmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZJqDNWAAAAHXRSTlMA4cAgkp9gUPjqyW9DKN3VG/PYtqqMioF0TDoVDFJ8yCYAAACpSURBVCjPzc3bDoMgEEXRY4E6oAjer/P/v1msYogxfe56mJxkJ4CD74haa1uiziOxVbXsddDLutqSUPACWCILLFwkQen9GrNfrZIw8hru6xXOyiNOQoiSTSmEMWEZDksAKDU/0CUq946a5pquAk+Iwh/RxODiKRQ/AsmnIAmSs4jomiyBgZU8ZNk5FA8Achb3pwTnfxzsPdhv8OTUQetzOPIIZpPdmBn4AGBDDL1sKHliAAAAAElFTkSuQmCC');
}
.scratchCard-dialog .scratchCard-content .scratchCard-bd .m-footer .rule[data-v-4e5e9886] {
  background-image: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAMAAADXqc3KAAAAZlBMVEUAAABmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmaud1slAAAAIXRSTlMArpYi+b0Jey1e9eDnKRPR0MHvqI+BPB0Ex7eda2VTSjb8J2hMAAAA5ElEQVQoz31SWRaDMAiErDZqtLvdy/0vWYyI/th85M1jgJlAQI6NviFqfLQSkDAS1WgM1kS4onbJmWqClXFpN8cjddWS1XYUJZ98mFDI5fZUamzqgmQ4uo8wdGnUQddKkwPRlFs55AIyEh8cE68CDVlWVuETe86TN9b3Nei5vLOg2kOD4uiJ+9McB2xgljiO0nsGIqJEAM9MUEJbwZVNpUFbLeKGCx6g4mo3c4GzDMSuPvDCBcdF2+pI2tHU+QMFOyxD7IuXMzO30jb0ya7Hnr9X0LHLovp2taie4tZq/36Gze/zA+HnDtRV4rygAAAAAElFTkSuQmCC');
}
@-webkit-keyframes loading-data-v-4e5e9886 {
from {
    -webkit-transform: translate(-50%, -50%) rotate(0deg);
    transform: translate(-50%, -50%) rotate(0deg);
}
to {
    -webkit-transform: translate(-50%, -50%) rotate(360deg);
    transform: translate(-50%, -50%) rotate(360deg);
}
}
@keyframes loading-data-v-4e5e9886 {
from {
    -webkit-transform: translate(-50%, -50%) rotate(0deg);
    transform: translate(-50%, -50%) rotate(0deg);
}
to {
    -webkit-transform: translate(-50%, -50%) rotate(360deg);
    transform: translate(-50%, -50%) rotate(360deg);
}
}
.scratchCard-dialog .scratchCard-content .scratchCard-tips[data-v-4e5e9886] {
  font-family: PingFangSC-Semibold;
  font-size: 0.346667rem;
  color: #ffffff;
  letter-spacing: 0;
  line-height: 0.426667rem;
  text-align: left;
  padding: 0.453333rem 0 0.453333rem 0.4rem;
}
.scratchCard-dialog .scratchCard-content .scratchCard-tips span[data-v-4e5e9886] {
  font-size: 0.426667rem;
}
@-webkit-keyframes slideInDown-data-v-4e5e9886 {
from {
    -webkit-transform: translate3d(0, -200%, 0);
    transform: translate3d(0, -200%, 0);
    visibility: visible;
}
to {
    -webkit-transform: translate3d(0, -40%, 0);
    transform: translate3d(0, -40%, 0);
}
}
@keyframes slideInDown-data-v-4e5e9886 {
from {
    -webkit-transform: translate3d(0, -200%, 0);
    transform: translate3d(0, -200%, 0);
    visibility: visible;
}
to {
    -webkit-transform: translate3d(0, -40%, 0);
    transform: translate3d(0, -40%, 0);
}
}
.plugin-tiger[data-v-98200b32] {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  margin: auto;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  z-index: 150;
  -webkit-transform: translateZ(1.32rem);
  transform: translateZ(1.32rem);
}
.tiger-container[data-v-98200b32] {
  position: absolute;
  top: 5.546667rem;
  left: 0.4rem;
  width: 9.28rem;
  height: 6.733333rem;
}
.tiger-container[data-v-98200b32]:after {
  content: '';
  width: 5.866667rem;
  height: 2.373333rem;
  position: absolute;
  top: -2.053333rem;
  left: 1.853333rem;
  background: url(//yun.duiba.com.cn/h5/sign_custom/skins/normal-calendar-sign_190826/image/tiger-icon.e67b93e5.png) no-repeat top center;
  background-size: 100% 100%;
}
.tiger-container .head-tip[data-v-98200b32],
.tiger-container .credits[data-v-98200b32] {
  font-size: 0.32rem;
  position: absolute;
  width: 100%;
  text-align: center;
  z-index: 1;
}
.tiger-container .head-tip[data-v-98200b32] {
  opacity: 0.9;
  font-family: PingFangSC-Regular;
  font-size: 0.373333rem;
  color: #ffffff;
  letter-spacing: 0;
  line-height: 0.533333rem;
  text-align: center;
  margin-top: 0.173333rem;
}
.tiger-container .lamp[data-v-98200b32] {
  width: 8.306667rem;
  height: 3.093333rem;
  background: url(//yun.duiba.com.cn/h5/sign_custom/skins/normal-calendar-sign_190826/image/lamp1.c028dd88.png) no-repeat top center;
  background-size: 100% 100%;
  position: absolute;
  left: 0.44rem;
  top: 0.986667rem;
  z-index: 1;
}
.tiger-container .lamp.active[data-v-98200b32] {
  -webkit-animation: lampTiger-data-v-98200b32 1s linear infinite;
  animation: lampTiger-data-v-98200b32 1s linear infinite;
}
.tiger-container .lamp2[data-v-98200b32] {
  background: url(//yun.duiba.com.cn/h5/sign_custom/skins/normal-calendar-sign_190826/image/lamp2.393316d9.png) no-repeat top center;
  background-size: 100% 100%;
}
.tiger-container .lamp2.active[data-v-98200b32] {
  -webkit-animation: lampTiger-data-v-98200b32 1s linear infinite;
  animation: lampTiger-data-v-98200b32 1s linear infinite;
  -webkit-animation-delay: 0.5s;
  animation-delay: 0.5s;
}
.tiger-container .slot-default[data-v-98200b32] {
  width: 8.28rem;
  height: 3.08rem;
  position: absolute;
  left: 0.866667rem;
  top: 1.053333rem;
  z-index: 3;
  padding-left: 0.173333rem;
  padding-top: 0.05rem;
}
.tiger-container .slot-default .prize[data-v-98200b32] {
  display: inline-block;
  width: 0.86rem;
  height: 0.75rem;
  text-align: center;
  background-image: -webkit-gradient(linear, left top, left bottom, from(#313131), color-stop(14%, rgba(255, 255, 255, 0.03)), color-stop(86%, rgba(255, 255, 255, 0)), to(#313131));
  background-image: -webkit-linear-gradient(top, #313131 0%, rgba(255, 255, 255, 0.03) 14%, rgba(255, 255, 255, 0) 86%, #313131 100%);
  background-image: -o-linear-gradient(top, #313131 0%, rgba(255, 255, 255, 0.03) 14%, rgba(255, 255, 255, 0) 86%, #313131 100%);
  background-image: linear-gradient(-180deg, #313131 0%, rgba(255, 255, 255, 0.03) 14%, rgba(255, 255, 255, 0) 86%, #313131 100%);
  border-radius: 0.1rem;
  background-color: #fff;
  margin: 0.125rem 0;
}
.tiger-container .slot-default .prize1[data-v-98200b32] {
  left: 2.48rem;
}
.tiger-container .slot-default .prize1[data-v-98200b32] {
  left: 4.96rem;
}
.tiger-container .tips[data-v-98200b32] {
  opacity: 0.9;
  font-family: PingFangSC-Regular;
  font-size: 0.32rem;
  color: #ffffff;
  letter-spacing: 0;
  text-align: center;
  line-height: 0.44rem;
  margin-top: -0.56rem;
}
.tiger-container .close[data-v-98200b32] {
  background: url(//yun.duiba.com.cn/h5/sign_custom/skins/normal-calendar-sign_190826/image/close-btn.ea52c800.png) no-repeat top center;
  background-size: 100% 100%;
  width: 0.853333rem;
  height: 0.853333rem;
  margin: 1.306667rem auto 0;
}
.tiger-container .start-tiger[data-v-98200b32] {
  position: absolute;
  width: 5.866667rem;
  height: 1.106667rem;
  cursor: pointer;
  background-color: transparent;
  background-image: url('//yun.duiba.com.cn/h5/signin/calendar/sougou/tiger-btn2.png');
  background-size: 6.133333rem 5.493333rem;
  background-position: -0.133333rem -1.506667rem;
  left: 50%;
  -webkit-transform: translateX(-50%);
  -ms-transform: translateX(-50%);
  transform: translateX(-50%);
  top: 4.453333rem;
}
.tiger-container .start-tiger.disabled[data-v-98200b32] {
  cursor: not-allowed;
  background-position: -0.133333rem -0.133333rem;
}
.tiger-container .start-tiger[data-v-98200b32]:active {
  opacity: 0.8;
}
.tiger-container .slot-machine[data-v-98200b32] {
  position: relative;
  overflow: hidden;
  width: 9.28rem;
  height: 6.733333rem;
  margin: 0 auto;
  background-repeat: no-repeat;
  background-position: 0 50%;
  background: url(//yun.duiba.com.cn/h5/sign_custom/skins/normal-calendar-sign_190826/image/tiger-bg.2322539c.png) no-repeat top center;
  background-size: 100% 100%;
}
.tiger-container .slot-machine[data-v-98200b32] .slot-machine-outer {
  position: absolute;
  top: 1.466667rem;
  left: 0.986667rem;
  overflow: hidden;
  width: 7.466667rem;
  height: 2.133333rem;
  border-radius: 0.2rem;
  -webkit-mask-image: -webkit-radial-gradient(white, black);
}
.tiger-container .slot-machine[data-v-98200b32] .slot-machine-inner {
  position: absolute;
  top: -2.546667rem !important;
}
.tiger-container .slot-machine[data-v-98200b32] .reel .tile {
  width: 2.293333rem;
  height: 2rem;
  text-align: center;
  background-image: -webkit-gradient(linear, left top, left bottom, from(rgba(255, 0, 0, 0.2)), color-stop(19%, rgba(255, 255, 255, 0.03)), color-stop(77%, rgba(255, 255, 255, 0)), to(rgba(255, 0, 0, 0.2)));
  background-image: -webkit-linear-gradient(top, rgba(255, 0, 0, 0.2) 0%, rgba(255, 255, 255, 0.03) 19%, rgba(255, 255, 255, 0) 77%, rgba(255, 0, 0, 0.2) 100%);
  background-image: -o-linear-gradient(top, rgba(255, 0, 0, 0.2) 0%, rgba(255, 255, 255, 0.03) 19%, rgba(255, 255, 255, 0) 77%, rgba(255, 0, 0, 0.2) 100%);
  background-image: linear-gradient(-180deg, rgba(255, 0, 0, 0.2) 0%, rgba(255, 255, 255, 0.03) 19%, rgba(255, 255, 255, 0) 77%, rgba(255, 0, 0, 0.2) 100%);
  border-radius: 0.266667rem;
  background-color: #fff;
  margin: 0.333333rem 0;
  -webkit-box-shadow: 0 0.08rem 0 #ef8686;
  box-shadow: 0 0.08rem 0 #ef8686;
}
.tiger-container .slot-machine[data-v-98200b32] .reel img {
  margin-top: 0.205rem;
  width: 1.54rem;
}
.tiger-container .slot-machine[data-v-98200b32] .reel {
  position: absolute;
  width: 2.293333rem;
}
.tiger-container .slot-machine[data-v-98200b32] .reel-2 {
  left: 2.48rem;
}
.tiger-container .slot-machine[data-v-98200b32] .reel-3 {
  left: 4.96rem;
}
@-webkit-keyframes lampTiger-data-v-98200b32 {
from {
    opacity: 1;
}
49.9% {
    opacity: 1;
}
50% {
    opacity: 0;
}
99.9% {
    opacity: 0;
}
to {
    opacity: 1;
}
}
@keyframes lampTiger-data-v-98200b32 {
from {
    opacity: 1;
}
49.9% {
    opacity: 1;
}
50% {
    opacity: 0;
}
99.9% {
    opacity: 0;
}
to {
    opacity: 1;
}
}
.unit-page .solid-line[data-v-2e9184f4] {
  width: 100%;
  height: 0.266667rem;
  background: #f3f3f3;
}
/**
 * Swiper 4.5.0
 * Most modern mobile touch slider and framework with hardware accelerated transitions
 * http://www.idangero.us/swiper/
 *
 * Copyright 2014-2019 Vladimir Kharlampidi
 *
 * Released under the MIT License
 *
 * Released on: February 22, 2019
 */
.swiper-container {
  margin: 0 auto;
  position: relative;
  overflow: hidden;
  list-style: none;
  padding: 0;
  /* Fix of Webkit flickering */
  z-index: 1;
}
.swiper-container-no-flexbox .swiper-slide {
  float: left;
}
.swiper-container-vertical > .swiper-wrapper {
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
}
.swiper-wrapper {
  position: relative;
  width: 100%;
  height: 100%;
  z-index: 1;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-transition-property: -webkit-transform;
  transition-property: -webkit-transform;
  -o-transition-property: transform;
  transition-property: transform;
  transition-property: transform, -webkit-transform;
  -webkit-box-sizing: content-box;
  box-sizing: content-box;
}
.swiper-container-android .swiper-slide,
.swiper-wrapper {
  -webkit-transform: translate3d(0px, 0, 0);
  transform: translate3d(0px, 0, 0);
}
.swiper-container-multirow > .swiper-wrapper {
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}
.swiper-container-free-mode > .swiper-wrapper {
  -webkit-transition-timing-function: ease-out;
  -o-transition-timing-function: ease-out;
  transition-timing-function: ease-out;
  margin: 0 auto;
}
.swiper-slide {
  -webkit-flex-shrink: 0;
  -ms-flex-negative: 0;
  flex-shrink: 0;
  width: 100%;
  height: 100%;
  position: relative;
  -webkit-transition-property: -webkit-transform;
  transition-property: -webkit-transform;
  -o-transition-property: transform;
  transition-property: transform;
  transition-property: transform, -webkit-transform;
}
.swiper-slide-invisible-blank {
  visibility: hidden;
}
/* Auto Height */
.swiper-container-autoheight,
.swiper-container-autoheight .swiper-slide {
  height: auto;
}
.swiper-container-autoheight .swiper-wrapper {
  -webkit-box-align: start;
  -webkit-align-items: flex-start;
  -ms-flex-align: start;
  align-items: flex-start;
  -webkit-transition-property: height, -webkit-transform;
  transition-property: height, -webkit-transform;
  -o-transition-property: transform, height;
  transition-property: transform, height;
  transition-property: transform, height, -webkit-transform;
}
/* 3D Effects */
.swiper-container-3d {
  -webkit-perspective: 1200px;
  perspective: 1200px;
}
.swiper-container-3d .swiper-wrapper,
.swiper-container-3d .swiper-slide,
.swiper-container-3d .swiper-slide-shadow-left,
.swiper-container-3d .swiper-slide-shadow-right,
.swiper-container-3d .swiper-slide-shadow-top,
.swiper-container-3d .swiper-slide-shadow-bottom,
.swiper-container-3d .swiper-cube-shadow {
  -webkit-transform-style: preserve-3d;
  transform-style: preserve-3d;
}
.swiper-container-3d .swiper-slide-shadow-left,
.swiper-container-3d .swiper-slide-shadow-right,
.swiper-container-3d .swiper-slide-shadow-top,
.swiper-container-3d .swiper-slide-shadow-bottom {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 10;
}
.swiper-container-3d .swiper-slide-shadow-left {
  background-image: -webkit-gradient(linear, right top, left top, from(rgba(0, 0, 0, 0.5)), to(rgba(0, 0, 0, 0)));
  background-image: -webkit-linear-gradient(right, rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0));
  background-image: -o-linear-gradient(right, rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0));
  background-image: linear-gradient(to left, rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0));
}
.swiper-container-3d .swiper-slide-shadow-right {
  background-image: -webkit-gradient(linear, left top, right top, from(rgba(0, 0, 0, 0.5)), to(rgba(0, 0, 0, 0)));
  background-image: -webkit-linear-gradient(left, rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0));
  background-image: -o-linear-gradient(left, rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0));
  background-image: linear-gradient(to right, rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0));
}
.swiper-container-3d .swiper-slide-shadow-top {
  background-image: -webkit-gradient(linear, left bottom, left top, from(rgba(0, 0, 0, 0.5)), to(rgba(0, 0, 0, 0)));
  background-image: -webkit-linear-gradient(bottom, rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0));
  background-image: -o-linear-gradient(bottom, rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0));
  background-image: linear-gradient(to top, rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0));
}
.swiper-container-3d .swiper-slide-shadow-bottom {
  background-image: -webkit-gradient(linear, left top, left bottom, from(rgba(0, 0, 0, 0.5)), to(rgba(0, 0, 0, 0)));
  background-image: -webkit-linear-gradient(top, rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0));
  background-image: -o-linear-gradient(top, rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0));
  background-image: linear-gradient(to bottom, rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0));
}
/* IE10 Windows Phone 8 Fixes */
.swiper-container-wp8-horizontal,
.swiper-container-wp8-horizontal > .swiper-wrapper {
  -ms-touch-action: pan-y;
  touch-action: pan-y;
}
.swiper-container-wp8-vertical,
.swiper-container-wp8-vertical > .swiper-wrapper {
  -ms-touch-action: pan-x;
  touch-action: pan-x;
}
.swiper-button-prev,
.swiper-button-next {
  position: absolute;
  top: 50%;
  width: 27px;
  height: 44px;
  margin-top: -22px;
  z-index: 10;
  cursor: pointer;
  background-size: 27px 44px;
  background-position: center;
  background-repeat: no-repeat;
}
.swiper-button-prev.swiper-button-disabled,
.swiper-button-next.swiper-button-disabled {
  opacity: 0.35;
  cursor: auto;
  pointer-events: none;
}
.swiper-button-prev,
.swiper-container-rtl .swiper-button-next {
  background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg%20xmlns%3D'http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg'%20viewBox%3D'0%200%2027%2044'%3E%3Cpath%20d%3D'M0%2C22L22%2C0l2.1%2C2.1L4.2%2C22l19.9%2C19.9L22%2C44L0%2C22L0%2C22L0%2C22z'%20fill%3D'%23007aff'%2F%3E%3C%2Fsvg%3E");
  left: 10px;
  right: auto;
}
.swiper-button-next,
.swiper-container-rtl .swiper-button-prev {
  background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg%20xmlns%3D'http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg'%20viewBox%3D'0%200%2027%2044'%3E%3Cpath%20d%3D'M27%2C22L27%2C22L5%2C44l-2.1-2.1L22.8%2C22L2.9%2C2.1L5%2C0L27%2C22L27%2C22z'%20fill%3D'%23007aff'%2F%3E%3C%2Fsvg%3E");
  right: 10px;
  left: auto;
}
.swiper-button-prev.swiper-button-white,
.swiper-container-rtl .swiper-button-next.swiper-button-white {
  background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg%20xmlns%3D'http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg'%20viewBox%3D'0%200%2027%2044'%3E%3Cpath%20d%3D'M0%2C22L22%2C0l2.1%2C2.1L4.2%2C22l19.9%2C19.9L22%2C44L0%2C22L0%2C22L0%2C22z'%20fill%3D'%23ffffff'%2F%3E%3C%2Fsvg%3E");
}
.swiper-button-next.swiper-button-white,
.swiper-container-rtl .swiper-button-prev.swiper-button-white {
  background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg%20xmlns%3D'http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg'%20viewBox%3D'0%200%2027%2044'%3E%3Cpath%20d%3D'M27%2C22L27%2C22L5%2C44l-2.1-2.1L22.8%2C22L2.9%2C2.1L5%2C0L27%2C22L27%2C22z'%20fill%3D'%23ffffff'%2F%3E%3C%2Fsvg%3E");
}
.swiper-button-prev.swiper-button-black,
.swiper-container-rtl .swiper-button-next.swiper-button-black {
  background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg%20xmlns%3D'http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg'%20viewBox%3D'0%200%2027%2044'%3E%3Cpath%20d%3D'M0%2C22L22%2C0l2.1%2C2.1L4.2%2C22l19.9%2C19.9L22%2C44L0%2C22L0%2C22L0%2C22z'%20fill%3D'%23000000'%2F%3E%3C%2Fsvg%3E");
}
.swiper-button-next.swiper-button-black,
.swiper-container-rtl .swiper-button-prev.swiper-button-black {
  background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg%20xmlns%3D'http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg'%20viewBox%3D'0%200%2027%2044'%3E%3Cpath%20d%3D'M27%2C22L27%2C22L5%2C44l-2.1-2.1L22.8%2C22L2.9%2C2.1L5%2C0L27%2C22L27%2C22z'%20fill%3D'%23000000'%2F%3E%3C%2Fsvg%3E");
}
.swiper-button-lock {
  display: none;
}
.swiper-pagination {
  position: absolute;
  text-align: center;
  -webkit-transition: 300ms opacity;
  -o-transition: 300ms opacity;
  transition: 300ms opacity;
  -webkit-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
  z-index: 10;
}
.swiper-pagination.swiper-pagination-hidden {
  opacity: 0;
}
/* Common Styles */
.swiper-pagination-fraction,
.swiper-pagination-custom,
.swiper-container-horizontal > .swiper-pagination-bullets {
  bottom: 10px;
  left: 0;
  width: 100%;
}
/* Bullets */
.swiper-pagination-bullets-dynamic {
  overflow: hidden;
  font-size: 0;
}
.swiper-pagination-bullets-dynamic .swiper-pagination-bullet {
  -webkit-transform: scale(0.33);
  -ms-transform: scale(0.33);
  transform: scale(0.33);
  position: relative;
}
.swiper-pagination-bullets-dynamic .swiper-pagination-bullet-active {
  -webkit-transform: scale(1);
  -ms-transform: scale(1);
  transform: scale(1);
}
.swiper-pagination-bullets-dynamic .swiper-pagination-bullet-active-main {
  -webkit-transform: scale(1);
  -ms-transform: scale(1);
  transform: scale(1);
}
.swiper-pagination-bullets-dynamic .swiper-pagination-bullet-active-prev {
  -webkit-transform: scale(0.66);
  -ms-transform: scale(0.66);
  transform: scale(0.66);
}
.swiper-pagination-bullets-dynamic .swiper-pagination-bullet-active-prev-prev {
  -webkit-transform: scale(0.33);
  -ms-transform: scale(0.33);
  transform: scale(0.33);
}
.swiper-pagination-bullets-dynamic .swiper-pagination-bullet-active-next {
  -webkit-transform: scale(0.66);
  -ms-transform: scale(0.66);
  transform: scale(0.66);
}
.swiper-pagination-bullets-dynamic .swiper-pagination-bullet-active-next-next {
  -webkit-transform: scale(0.33);
  -ms-transform: scale(0.33);
  transform: scale(0.33);
}
.swiper-pagination-bullet {
  width: 8px;
  height: 8px;
  display: inline-block;
  border-radius: 100%;
  background: #000;
  opacity: 0.2;
}
button.swiper-pagination-bullet {
  border: none;
  margin: 0;
  padding: 0;
  -webkit-box-shadow: none;
  box-shadow: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
}
.swiper-pagination-clickable .swiper-pagination-bullet {
  cursor: pointer;
}
.swiper-pagination-bullet-active {
  opacity: 1;
  background: #007aff;
}
.swiper-container-vertical > .swiper-pagination-bullets {
  right: 10px;
  top: 50%;
  -webkit-transform: translate3d(0px, -50%, 0);
  transform: translate3d(0px, -50%, 0);
}
.swiper-container-vertical > .swiper-pagination-bullets .swiper-pagination-bullet {
  margin: 6px 0;
  display: block;
}
.swiper-container-vertical > .swiper-pagination-bullets.swiper-pagination-bullets-dynamic {
  top: 50%;
  -webkit-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  transform: translateY(-50%);
  width: 8px;
}
.swiper-container-vertical > .swiper-pagination-bullets.swiper-pagination-bullets-dynamic .swiper-pagination-bullet {
  display: inline-block;
  -webkit-transition: 200ms top, 200ms -webkit-transform;
  transition: 200ms top, 200ms -webkit-transform;
  -o-transition: 200ms transform, 200ms top;
  transition: 200ms transform, 200ms top;
  transition: 200ms transform, 200ms top, 200ms -webkit-transform;
}
.swiper-container-horizontal > .swiper-pagination-bullets .swiper-pagination-bullet {
  margin: 0 4px;
}
.swiper-container-horizontal > .swiper-pagination-bullets.swiper-pagination-bullets-dynamic {
  left: 50%;
  -webkit-transform: translateX(-50%);
  -ms-transform: translateX(-50%);
  transform: translateX(-50%);
  white-space: nowrap;
}
.swiper-container-horizontal > .swiper-pagination-bullets.swiper-pagination-bullets-dynamic .swiper-pagination-bullet {
  -webkit-transition: 200ms left, 200ms -webkit-transform;
  transition: 200ms left, 200ms -webkit-transform;
  -o-transition: 200ms transform, 200ms left;
  transition: 200ms transform, 200ms left;
  transition: 200ms transform, 200ms left, 200ms -webkit-transform;
}
.swiper-container-horizontal.swiper-container-rtl > .swiper-pagination-bullets-dynamic .swiper-pagination-bullet {
  -webkit-transition: 200ms right, 200ms -webkit-transform;
  transition: 200ms right, 200ms -webkit-transform;
  -o-transition: 200ms transform, 200ms right;
  transition: 200ms transform, 200ms right;
  transition: 200ms transform, 200ms right, 200ms -webkit-transform;
}
/* Progress */
.swiper-pagination-progressbar {
  background: rgba(0, 0, 0, 0.25);
  position: absolute;
}
.swiper-pagination-progressbar .swiper-pagination-progressbar-fill {
  background: #007aff;
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  -webkit-transform: scale(0);
  -ms-transform: scale(0);
  transform: scale(0);
  -webkit-transform-origin: left top;
  -ms-transform-origin: left top;
  transform-origin: left top;
}
.swiper-container-rtl .swiper-pagination-progressbar .swiper-pagination-progressbar-fill {
  -webkit-transform-origin: right top;
  -ms-transform-origin: right top;
  transform-origin: right top;
}
.swiper-container-horizontal > .swiper-pagination-progressbar,
.swiper-container-vertical > .swiper-pagination-progressbar.swiper-pagination-progressbar-opposite {
  width: 100%;
  height: 4px;
  left: 0;
  top: 0;
}
.swiper-container-vertical > .swiper-pagination-progressbar,
.swiper-container-horizontal > .swiper-pagination-progressbar.swiper-pagination-progressbar-opposite {
  width: 4px;
  height: 100%;
  left: 0;
  top: 0;
}
.swiper-pagination-white .swiper-pagination-bullet-active {
  background: #ffffff;
}
.swiper-pagination-progressbar.swiper-pagination-white {
  background: rgba(255, 255, 255, 0.25);
}
.swiper-pagination-progressbar.swiper-pagination-white .swiper-pagination-progressbar-fill {
  background: #ffffff;
}
.swiper-pagination-black .swiper-pagination-bullet-active {
  background: #000000;
}
.swiper-pagination-progressbar.swiper-pagination-black {
  background: rgba(0, 0, 0, 0.25);
}
.swiper-pagination-progressbar.swiper-pagination-black .swiper-pagination-progressbar-fill {
  background: #000000;
}
.swiper-pagination-lock {
  display: none;
}
/* Scrollbar */
.swiper-scrollbar {
  border-radius: 10px;
  position: relative;
  -ms-touch-action: none;
  background: rgba(0, 0, 0, 0.1);
}
.swiper-container-horizontal > .swiper-scrollbar {
  position: absolute;
  left: 1%;
  bottom: 3px;
  z-index: 50;
  height: 5px;
  width: 98%;
}
.swiper-container-vertical > .swiper-scrollbar {
  position: absolute;
  right: 3px;
  top: 1%;
  z-index: 50;
  width: 5px;
  height: 98%;
}
.swiper-scrollbar-drag {
  height: 100%;
  width: 100%;
  position: relative;
  background: rgba(0, 0, 0, 0.5);
  border-radius: 10px;
  left: 0;
  top: 0;
}
.swiper-scrollbar-cursor-drag {
  cursor: move;
}
.swiper-scrollbar-lock {
  display: none;
}
.swiper-zoom-container {
  width: 100%;
  height: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -webkit-align-items: center;
  -ms-flex-align: center;
  align-items: center;
  text-align: center;
}
.swiper-zoom-container > img,
.swiper-zoom-container > svg,
.swiper-zoom-container > canvas {
  max-width: 100%;
  max-height: 100%;
  -o-object-fit: contain;
  object-fit: contain;
}
.swiper-slide-zoomed {
  cursor: move;
}
/* Preloader */
.swiper-lazy-preloader {
  width: 42px;
  height: 42px;
  position: absolute;
  left: 50%;
  top: 50%;
  margin-left: -21px;
  margin-top: -21px;
  z-index: 10;
  -webkit-transform-origin: 50%;
  -ms-transform-origin: 50%;
  transform-origin: 50%;
  -webkit-animation: swiper-preloader-spin 1s steps(12, end) infinite;
  animation: swiper-preloader-spin 1s steps(12, end) infinite;
}
.swiper-lazy-preloader:after {
  display: block;
  content: '';
  width: 100%;
  height: 100%;
  background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg%20viewBox%3D'0%200%20120%20120'%20xmlns%3D'http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg'%20xmlns%3Axlink%3D'http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink'%3E%3Cdefs%3E%3Cline%20id%3D'l'%20x1%3D'60'%20x2%3D'60'%20y1%3D'7'%20y2%3D'27'%20stroke%3D'%236c6c6c'%20stroke-width%3D'11'%20stroke-linecap%3D'round'%2F%3E%3C%2Fdefs%3E%3Cg%3E%3Cuse%20xlink%3Ahref%3D'%23l'%20opacity%3D'.27'%2F%3E%3Cuse%20xlink%3Ahref%3D'%23l'%20opacity%3D'.27'%20transform%3D'rotate(30%2060%2C60)'%2F%3E%3Cuse%20xlink%3Ahref%3D'%23l'%20opacity%3D'.27'%20transform%3D'rotate(60%2060%2C60)'%2F%3E%3Cuse%20xlink%3Ahref%3D'%23l'%20opacity%3D'.27'%20transform%3D'rotate(90%2060%2C60)'%2F%3E%3Cuse%20xlink%3Ahref%3D'%23l'%20opacity%3D'.27'%20transform%3D'rotate(120%2060%2C60)'%2F%3E%3Cuse%20xlink%3Ahref%3D'%23l'%20opacity%3D'.27'%20transform%3D'rotate(150%2060%2C60)'%2F%3E%3Cuse%20xlink%3Ahref%3D'%23l'%20opacity%3D'.37'%20transform%3D'rotate(180%2060%2C60)'%2F%3E%3Cuse%20xlink%3Ahref%3D'%23l'%20opacity%3D'.46'%20transform%3D'rotate(210%2060%2C60)'%2F%3E%3Cuse%20xlink%3Ahref%3D'%23l'%20opacity%3D'.56'%20transform%3D'rotate(240%2060%2C60)'%2F%3E%3Cuse%20xlink%3Ahref%3D'%23l'%20opacity%3D'.66'%20transform%3D'rotate(270%2060%2C60)'%2F%3E%3Cuse%20xlink%3Ahref%3D'%23l'%20opacity%3D'.75'%20transform%3D'rotate(300%2060%2C60)'%2F%3E%3Cuse%20xlink%3Ahref%3D'%23l'%20opacity%3D'.85'%20transform%3D'rotate(330%2060%2C60)'%2F%3E%3C%2Fg%3E%3C%2Fsvg%3E");
  background-position: 50%;
  background-size: 100%;
  background-repeat: no-repeat;
}
.swiper-lazy-preloader-white:after {
  background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg%20viewBox%3D'0%200%20120%20120'%20xmlns%3D'http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg'%20xmlns%3Axlink%3D'http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink'%3E%3Cdefs%3E%3Cline%20id%3D'l'%20x1%3D'60'%20x2%3D'60'%20y1%3D'7'%20y2%3D'27'%20stroke%3D'%23fff'%20stroke-width%3D'11'%20stroke-linecap%3D'round'%2F%3E%3C%2Fdefs%3E%3Cg%3E%3Cuse%20xlink%3Ahref%3D'%23l'%20opacity%3D'.27'%2F%3E%3Cuse%20xlink%3Ahref%3D'%23l'%20opacity%3D'.27'%20transform%3D'rotate(30%2060%2C60)'%2F%3E%3Cuse%20xlink%3Ahref%3D'%23l'%20opacity%3D'.27'%20transform%3D'rotate(60%2060%2C60)'%2F%3E%3Cuse%20xlink%3Ahref%3D'%23l'%20opacity%3D'.27'%20transform%3D'rotate(90%2060%2C60)'%2F%3E%3Cuse%20xlink%3Ahref%3D'%23l'%20opacity%3D'.27'%20transform%3D'rotate(120%2060%2C60)'%2F%3E%3Cuse%20xlink%3Ahref%3D'%23l'%20opacity%3D'.27'%20transform%3D'rotate(150%2060%2C60)'%2F%3E%3Cuse%20xlink%3Ahref%3D'%23l'%20opacity%3D'.37'%20transform%3D'rotate(180%2060%2C60)'%2F%3E%3Cuse%20xlink%3Ahref%3D'%23l'%20opacity%3D'.46'%20transform%3D'rotate(210%2060%2C60)'%2F%3E%3Cuse%20xlink%3Ahref%3D'%23l'%20opacity%3D'.56'%20transform%3D'rotate(240%2060%2C60)'%2F%3E%3Cuse%20xlink%3Ahref%3D'%23l'%20opacity%3D'.66'%20transform%3D'rotate(270%2060%2C60)'%2F%3E%3Cuse%20xlink%3Ahref%3D'%23l'%20opacity%3D'.75'%20transform%3D'rotate(300%2060%2C60)'%2F%3E%3Cuse%20xlink%3Ahref%3D'%23l'%20opacity%3D'.85'%20transform%3D'rotate(330%2060%2C60)'%2F%3E%3C%2Fg%3E%3C%2Fsvg%3E");
}
@-webkit-keyframes swiper-preloader-spin {
  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}
@keyframes swiper-preloader-spin {
  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}
/* a11y */
.swiper-container .swiper-notification {
  position: absolute;
  left: 0;
  top: 0;
  pointer-events: none;
  opacity: 0;
  z-index: -1000;
}
.swiper-container-fade.swiper-container-free-mode .swiper-slide {
  -webkit-transition-timing-function: ease-out;
  -o-transition-timing-function: ease-out;
  transition-timing-function: ease-out;
}
.swiper-container-fade .swiper-slide {
  pointer-events: none;
  -webkit-transition-property: opacity;
  -o-transition-property: opacity;
  transition-property: opacity;
}
.swiper-container-fade .swiper-slide .swiper-slide {
  pointer-events: none;
}
.swiper-container-fade .swiper-slide-active,
.swiper-container-fade .swiper-slide-active .swiper-slide-active {
  pointer-events: auto;
}
.swiper-container-cube {
  overflow: visible;
}
.swiper-container-cube .swiper-slide {
  pointer-events: none;
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
  z-index: 1;
  visibility: hidden;
  -webkit-transform-origin: 0 0;
  -ms-transform-origin: 0 0;
  transform-origin: 0 0;
  width: 100%;
  height: 100%;
}
.swiper-container-cube .swiper-slide .swiper-slide {
  pointer-events: none;
}
.swiper-container-cube.swiper-container-rtl .swiper-slide {
  -webkit-transform-origin: 100% 0;
  -ms-transform-origin: 100% 0;
  transform-origin: 100% 0;
}
.swiper-container-cube .swiper-slide-active,
.swiper-container-cube .swiper-slide-active .swiper-slide-active {
  pointer-events: auto;
}
.swiper-container-cube .swiper-slide-active,
.swiper-container-cube .swiper-slide-next,
.swiper-container-cube .swiper-slide-prev,
.swiper-container-cube .swiper-slide-next + .swiper-slide {
  pointer-events: auto;
  visibility: visible;
}
.swiper-container-cube .swiper-slide-shadow-top,
.swiper-container-cube .swiper-slide-shadow-bottom,
.swiper-container-cube .swiper-slide-shadow-left,
.swiper-container-cube .swiper-slide-shadow-right {
  z-index: 0;
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
}
.swiper-container-cube .swiper-cube-shadow {
  position: absolute;
  left: 0;
  bottom: 0px;
  width: 100%;
  height: 100%;
  background: #000;
  opacity: 0.6;
  -webkit-filter: blur(50px);
  filter: blur(50px);
  z-index: 0;
}
.swiper-container-flip {
  overflow: visible;
}
.swiper-container-flip .swiper-slide {
  pointer-events: none;
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
  z-index: 1;
}
.swiper-container-flip .swiper-slide .swiper-slide {
  pointer-events: none;
}
.swiper-container-flip .swiper-slide-active,
.swiper-container-flip .swiper-slide-active .swiper-slide-active {
  pointer-events: auto;
}
.swiper-container-flip .swiper-slide-shadow-top,
.swiper-container-flip .swiper-slide-shadow-bottom,
.swiper-container-flip .swiper-slide-shadow-left,
.swiper-container-flip .swiper-slide-shadow-right {
  z-index: 0;
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
}
.swiper-container-coverflow .swiper-wrapper {
  /* Windows 8 IE 10 fix */
  -ms-perspective: 1200px;
}

.normal-page[data-v-5096c114],
.normal-page-content[data-v-5096c114] {
  font-size: 0.32rem;
  width: 100%;
  background-color: #fff;
  position: relative;
}
.opacity-4[data-v-5096c114] {
  opacity: 0.4;
}
.blur[data-v-5096c114] {
  -webkit-filter: blur(0.066667rem);
  filter: blur(0.066667rem);
}
.icon[data-v-5096c114] {
  width: 1em;
  height: 1em;
  vertical-align: -0.15em;
  fill: currentColor;
  overflow: hidden;
}
.scale-enter-active[data-v-5096c114] {
  -webkit-transition: opacity 0.3s, -webkit-transform 0.3s;
  transition: opacity 0.3s, -webkit-transform 0.3s;
  -o-transition: opacity 0.3s, transform 0.3s;
  transition: opacity 0.3s, transform 0.3s;
  transition: opacity 0.3s, transform 0.3s, -webkit-transform 0.3s;
}
.scale-enter-active > div[data-v-5096c114]:nth-of-type(1),
.scale-enter-active > div[data-v-5096c114]:nth-of-type(2),
.scale-enter-active > section[data-v-5096c114]:first-child {
  -webkit-animation: bounceIn-data-v-5096c114 0.3s linear alternate both;
  animation: bounceIn-data-v-5096c114 0.3s linear alternate both;
}
.scale-leave-active[data-v-5096c114] {
  -webkit-transition: opacity 0.2s, -webkit-transform 0.2s;
  transition: opacity 0.2s, -webkit-transform 0.2s;
  -o-transition: opacity 0.2s, transform 0.2s;
  transition: opacity 0.2s, transform 0.2s;
  transition: opacity 0.2s, transform 0.2s, -webkit-transform 0.2s;
}
.scale-leave-active > div[data-v-5096c114]:nth-of-type(1),
.scale-leave-active > div[data-v-5096c114]:nth-of-type(2),
.scale-leave-active > section[data-v-5096c114]:first-child {
  -webkit-animation: bounceOut-data-v-5096c114 0.2s linear alternate both;
  animation: bounceOut-data-v-5096c114 0.2s linear alternate both;
}
.scale-leave-to[data-v-5096c114] {
  opacity: 0;
}
.scale-leave-to > div[data-v-5096c114]:nth-of-type(1),
.scale-leave-to > div[data-v-5096c114]:nth-of-type(2),
.scale-leave-to > section[data-v-5096c114]:first-child {
  opacity: 0;
}
@-webkit-keyframes bounceIn-data-v-5096c114 {
0% {
    opacity: 0;
    -webkit-transform: scale(0.5);
    transform: scale(0.5);
}
100% {
    opacity: 1;
    -webkit-transform: scale(1);
    transform: scale(1);
}
}
@keyframes bounceIn-data-v-5096c114 {
0% {
    opacity: 0;
    -webkit-transform: scale(0.5);
    transform: scale(0.5);
}
100% {
    opacity: 1;
    -webkit-transform: scale(1);
    transform: scale(1);
}
}
@-webkit-keyframes bounceOut-data-v-5096c114 {
0% {
    opacity: 1;
    -webkit-transform: scale(1);
    transform: scale(1);
}
100% {
    opacity: 0;
    -webkit-transform: scale(0.5);
    transform: scale(0.5);
}
}
@keyframes bounceOut-data-v-5096c114 {
0% {
    opacity: 1;
    -webkit-transform: scale(1);
    transform: scale(1);
}
100% {
    opacity: 0;
    -webkit-transform: scale(0.5);
    transform: scale(0.5);
}
}
.danmu-swiper[data-v-5096c114] {
  width: 9.2rem;
  margin: 0.4rem auto 0;
  border-radius: 0.186667rem;
  padding: 0.266667rem;
  overflow: hidden;
  -webkit-mask-image: -webkit-radial-gradient(white, black);
}
.danmu-swiper .swiper-container[data-v-5096c114] {
  height: 0.48rem;
}
.danmu-swiper-box[data-v-5096c114] {
  white-space: nowrap;
  overflow: hidden;
  -o-text-overflow: ellipsis;
  text-overflow: ellipsis;
  line-height: 0.48rem;
  width: 100%;
}
.buoy-area-main[data-v-25b459e2] {
  position: absolute;
  z-index: 2;
  top: 2.666667rem;
  width: 1.866667rem;
  height: 0.933333rem;
  background-position: center;
  background-repeat: no-repeat;
  background-size: 100% 100%;
  background-image: url('//yun.dui88.com/h5/activity/duobaoSign/buoyIcon.png');
}
.rule-dialog-wrap[data-v-ee349518] {
  position: fixed;
  width: 100vw;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.6);
  z-index: 50;
  top: 0;
  left: 0;
  overflow: hidden;
}
.rule-dialog-content-wrap[data-v-ee349518] {
  position: relative;
  width: 8.293333rem;
  height: 5.786667rem;
  background-size: contain;
  margin: 0 auto;
  margin-top: 45%;
  background-image: url('https://yun.duiba.com.cn/jifen/images/tailong/b851c493-2d26-4367-90f9-bcc4baaed80c.png');
}
.rule-dialog-content-wrap .title-rule[data-v-ee349518] {
  font-family: PingFangSC-Medium;
  font-size: 0.56rem;
  color: #323233;
  text-align: center;
  padding-top: 0.4rem;
}
.rule-dialog-content-wrap .close-icon[data-v-ee349518] {
  position: absolute;
  height: 0.68rem;
  width: 0.68rem;
  left: 0;
  right: 0;
  bottom: -1.333333rem;
  margin: 0 auto;
}
.rule-dialog-content-wrap .operate-group[data-v-ee349518] {
  position: absolute;
  padding: 0 0.533333rem;
  width: 8.293333rem;
  bottom: 0.666667rem;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  -ms-flex-pack: justify;
  justify-content: space-between;
  margin: 0 auto;
}
.rule-dialog-content-wrap .operate-group .login[data-v-ee349518] {
  width: 3.306667rem;
  height: 0.96rem;
  line-height: 0.96rem;
  border: 0.026667rem solid #1bb9fa;
  border-radius: 0.48rem;
  font-family: PingFangSC-Regular;
  font-size: 0.373333rem;
  color: #0bb6fb;
  text-align: center;
  font-weight: 400;
}
.rule-dialog-content-wrap .operate-group .apply[data-v-ee349518] {
  width: 3.306667rem;
  height: 0.96rem;
  line-height: 0.96rem;
  background-image: -webkit-gradient(linear, left top, left bottom, from(#6acdfd), to(#00b3fa));
  background-image: -webkit-linear-gradient(top, #6acdfd 0%, #00b3fa 100%);
  background-image: -o-linear-gradient(top, #6acdfd 0%, #00b3fa 100%);
  background-image: linear-gradient(180deg, #6acdfd 0%, #00b3fa 100%);
  border-radius: 0.48rem;
  font-family: PingFangSC-Regular;
  font-size: 0.373333rem;
  color: #ffffff;
  text-align: center;
  font-weight: 400;
}
.rule-dialog-content-wrap .content[data-v-ee349518] {
  position: absolute;
  width: 6.96rem;
  font-family: PingFangSC-Regular;
  font-size: 0.426667rem;
  color: rgba(50, 50, 51, 0.8);
  text-align: center;
  left: 0;
  right: 0;
  top: 1.866667rem;
  padding: 0 0.4rem;
  margin: 0 auto;
  overflow-y: scroll;
  word-break: break-all;
}
.normal-page[data-v-4a8738bd],
.normal-page-content[data-v-4a8738bd] {
  font-size: 0.32rem;
  width: 100%;
  background-color: #fff;
  position: relative;
}
.opacity-4[data-v-4a8738bd] {
  opacity: 0.4;
}
.blur[data-v-4a8738bd] {
  -webkit-filter: blur(0.066667rem);
  filter: blur(0.066667rem);
}
.icon[data-v-4a8738bd] {
  width: 1em;
  height: 1em;
  vertical-align: -0.15em;
  fill: currentColor;
  overflow: hidden;
}
.scale-enter-active[data-v-4a8738bd] {
  -webkit-transition: opacity 0.3s, -webkit-transform 0.3s;
  transition: opacity 0.3s, -webkit-transform 0.3s;
  -o-transition: opacity 0.3s, transform 0.3s;
  transition: opacity 0.3s, transform 0.3s;
  transition: opacity 0.3s, transform 0.3s, -webkit-transform 0.3s;
}
.scale-enter-active > div[data-v-4a8738bd]:nth-of-type(1),
.scale-enter-active > div[data-v-4a8738bd]:nth-of-type(2),
.scale-enter-active > section[data-v-4a8738bd]:first-child {
  -webkit-animation: bounceIn-data-v-4a8738bd 0.3s linear alternate both;
  animation: bounceIn-data-v-4a8738bd 0.3s linear alternate both;
}
.scale-leave-active[data-v-4a8738bd] {
  -webkit-transition: opacity 0.2s, -webkit-transform 0.2s;
  transition: opacity 0.2s, -webkit-transform 0.2s;
  -o-transition: opacity 0.2s, transform 0.2s;
  transition: opacity 0.2s, transform 0.2s;
  transition: opacity 0.2s, transform 0.2s, -webkit-transform 0.2s;
}
.scale-leave-active > div[data-v-4a8738bd]:nth-of-type(1),
.scale-leave-active > div[data-v-4a8738bd]:nth-of-type(2),
.scale-leave-active > section[data-v-4a8738bd]:first-child {
  -webkit-animation: bounceOut-data-v-4a8738bd 0.2s linear alternate both;
  animation: bounceOut-data-v-4a8738bd 0.2s linear alternate both;
}
.scale-leave-to[data-v-4a8738bd] {
  opacity: 0;
}
.scale-leave-to > div[data-v-4a8738bd]:nth-of-type(1),
.scale-leave-to > div[data-v-4a8738bd]:nth-of-type(2),
.scale-leave-to > section[data-v-4a8738bd]:first-child {
  opacity: 0;
}
@-webkit-keyframes bounceIn-data-v-4a8738bd {
0% {
    opacity: 0;
    -webkit-transform: scale(0.5);
    transform: scale(0.5);
}
100% {
    opacity: 1;
    -webkit-transform: scale(1);
    transform: scale(1);
}
}
@keyframes bounceIn-data-v-4a8738bd {
0% {
    opacity: 0;
    -webkit-transform: scale(0.5);
    transform: scale(0.5);
}
100% {
    opacity: 1;
    -webkit-transform: scale(1);
    transform: scale(1);
}
}
@-webkit-keyframes bounceOut-data-v-4a8738bd {
0% {
    opacity: 1;
    -webkit-transform: scale(1);
    transform: scale(1);
}
100% {
    opacity: 0;
    -webkit-transform: scale(0.5);
    transform: scale(0.5);
}
}
@keyframes bounceOut-data-v-4a8738bd {
0% {
    opacity: 1;
    -webkit-transform: scale(1);
    transform: scale(1);
}
100% {
    opacity: 0;
    -webkit-transform: scale(0.5);
    transform: scale(0.5);
}
}
.normal-page-content[data-v-4a8738bd],
.middle-content[data-v-4a8738bd] {
  padding-bottom: 0.4rem;
}
.no-more[data-v-4a8738bd] {
  height: 0.44rem;
  font-size: 0.32rem;
  font-family: PingFangSC;
  font-weight: 400;
  color: #999999;
  line-height: 0.44rem;
  text-align: center;
}
.db-message {
  position: fixed;
  top: 5.6rem;
  z-index: 200;
  text-align: center;
  left: 0;
  right: 0;
  margin: 0 auto;
}
.db-message .content {
  display: inline-block;
  background-color: rgba(0, 0, 0, 0.7);
  color: #fff;
  font-size: 0.373333rem;
  border-radius: 0.186667rem;
  padding: 0.533333rem 0.4rem;
  max-width: 8rem;
  min-width: 4.4rem;
}
.db-message.slide-fade-enter-active {
  -webkit-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
}
.db-message.slide-fade-leave-active {
  -webkit-transition: all 0.5s cubic-bezier(1, 0.5, 0.8, 1);
  -o-transition: all 0.5s cubic-bezier(1, 0.5, 0.8, 1);
  transition: all 0.5s cubic-bezier(1, 0.5, 0.8, 1);
}
.db-message.slide-fade-enter,
.db-message.slide-fade-leave-to {
  -webkit-transform: translateY(0.266667rem);
  -ms-transform: translateY(0.266667rem);
  transform: translateY(0.266667rem);
  opacity: 0;
}
@charset "utf-8";

html {
  font-family: 'Helvetica Neue', Helvetica, Hiragino Sans GB, 'Microsoft Yahei', STHeiTi, Arial, sans-serif;
  color: #333;
  background: #fff;
  -webkit-text-size-adjust: 100%;
  -ms-text-size-adjust: 100%;
  -moz-text-size-adjust: 100%;
  text-size-adjust: 100%;
}

html * {
  outline: none;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  -webkit-text-size-adjust: none;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}

body {
  -webkit-font-smoothing: antialiased;
  -webkit-overflow-scrolling: touch;
  -webkit-touch-callout: none;
  font-size: 0.186667rem;
  line-height: 1.3;
  width: 100%;
  margin: 0 auto;
  color: #333;
  background-color: #f0f0f0;
}

body,
div,
dl,
dt,
dd,
ul,
ol,
li,
h1,
h2,
h3,
h4,
h5,
h6,
pre,
code,
form,
fieldset,
legend,
input,
textarea,
p,
blockquote,
th,
td,
hr,
button,
article,
aside,
details,
figcaption,
figure,
footer,
header,
hgroup,
menu,
nav,
section {
  margin: 0;
  padding: 0;
}

input,
select,
textarea {
  font-size: 100%;
}

input:-ms-clear {
  display: none;
}

table {
  border-collapse: collapse;
  border-spacing: 0;
}

fieldset,
img {
  border: 0;
}

abbr,
acronym {
  border: 0;
  font-variant: normal;
}

del {
  text-decoration: line-through;
}

address,
caption,
cite,
code,
dfn,
em,
th,
var {
  font-style: normal;
  font-weight: 500;
}

ol,
ul {
  list-style: none;
}

caption,
th {
  text-align: left;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  font-size: 100%;
  font-weight: 500;
}

q:before,
q:after {
  content: '';
}

sub,
sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}

sup {
  top: -0.5em;
}

sub {
  bottom: -0.25em;
}

a:active {
  text-decoration: none;
}

ins,
a {
  color: #333;
  text-decoration: none;
}

fieldset,
img,
button,
input {
  border: 0;
}

button,
input,
select,
textarea {
  font-size: 100%;
}

table {
  border-spacing: 0;
  border-collapse: collapse;
}

input {
  border-radius: 0;
  -webkit-appearance: none;
}

:focus {
  outline: 0;
}
