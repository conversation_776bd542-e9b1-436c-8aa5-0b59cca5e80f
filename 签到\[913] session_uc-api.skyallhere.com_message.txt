POST /miniprogram/api/v2/user/exchange HTTP/1.1
Host: uc-api.skyallhere.com
Connection: keep-alive
Content-Length: 43
App-Path: /pages/index/index
Authorization: Bearer
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF WindowsWechat(0x63090c37)XWEB/14315
Content-Type: application/json
App-System: Windows 10 x64
xweb_xhr: 1
App-Model: microsoft
App-Sdkversion: 3.9.0
App-Version: 3.9.12
Accept: */*
Sec-Fetch-Site: cross-site
Sec-Fetch-Mode: cors
Sec-Fetch-Dest: empty
Referer: https://servicewechat.com/wxff438d3c60c63fb6/342/page-frame.html
Accept-Encoding: gzip, deflate, br
Accept-Language: zh-CN,zh;q=0.9

{"code":"0a3kR1200etAKU1Ie4000F1BDq1kR121"}

HTTP/1.1 200 OK
Date: Tue, 05 Aug 2025 06:18:51 GMT
Content-Type: application/json; charset=utf-8
Content-Length: 74
Connection: keep-alive
X-Request-Id: e2627a5d277affa7c1d6394e5829e991
Access-Control-Allow-Origin: *
Access-Control-Allow-Credentials: true

{"code":0,"data":{"ticket":"f3351ccad465bd5c977d631d564afa50"},"msg":"ok"}