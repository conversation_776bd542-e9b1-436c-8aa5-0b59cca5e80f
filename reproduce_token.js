/**
 * 复现token生成 - 基于ASCII值分析
 */

console.log('=== Token复现分析 ===\n');

// 关键数据
const ACTUAL_TIMESTAMP = 1754383264519;
const ACTUAL_TOKEN = '7jdcpm';
const TARGET_ASCII = [55, 106, 100, 99, 112, 109]; // 7jdcpm的ASCII值
const XOR_ARRAY = [2212, 53, 2852, 3445, 3327];

console.log(`目标时间戳: ${ACTUAL_TIMESTAMP}`);
console.log(`目标token: ${ACTUAL_TOKEN}`);
console.log(`目标ASCII: [${TARGET_ASCII.join(', ')}]`);
console.log(`XOR数组: [${XOR_ARRAY.join(', ')}]\n`);

// 验证ASCII转换
console.log('验证ASCII转换:');
const reconstructed = String.fromCharCode(...TARGET_ASCII);
console.log(`String.fromCharCode(${TARGET_ASCII.join(', ')}) = "${reconstructed}"`);
console.log(`匹配: ${reconstructed === ACTUAL_TOKEN ? '✅' : '❌'}\n`);

// 尝试找到ASCII值的生成规律
function findASCIIPattern() {
    console.log('寻找ASCII值生成规律...\n');
    
    const timeStr = ACTUAL_TIMESTAMP.toString();
    console.log(`时间戳字符串: ${timeStr}`);
    
    // 尝试不同的计算方法
    for (let method = 1; method <= 20; method++) {
        console.log(`\n方法${method}:`);
        const calculatedASCII = [];
        let success = true;
        
        for (let i = 0; i < 6; i++) {
            let ascii = 0;
            const timeValue = parseInt(timeStr[i % timeStr.length]) || 0;
            const xorValue = XOR_ARRAY[i % XOR_ARRAY.length];
            const targetASCII = TARGET_ASCII[i];
            
            switch (method) {
                case 1: // 直接XOR
                    ascii = xorValue ^ timeValue;
                    break;
                    
                case 2: // XOR + 偏移
                    ascii = (xorValue ^ timeValue) + 50;
                    break;
                    
                case 3: // 时间戳分段
                    const segment = parseInt(timeStr.substring(i * 2, i * 2 + 2)) || timeValue;
                    ascii = xorValue ^ segment;
                    break;
                    
                case 4: // 复杂计算
                    ascii = (xorValue + timeValue * 13) % 256;
                    break;
                    
                case 5: // 基于位置的计算
                    ascii = (xorValue >> (i + 1)) + timeValue + 50;
                    break;
                    
                case 6: // 时间戳哈希
                    const hash = ACTUAL_TIMESTAMP % 1000;
                    ascii = (hash + xorValue + i * 7) % 256;
                    break;
                    
                case 7: // 简单加法
                    ascii = (timeValue + xorValue % 256) % 256;
                    break;
                    
                case 8: // 乘法组合
                    ascii = (timeValue * 7 + xorValue % 256) % 256;
                    break;
                    
                case 9: // 位运算组合
                    ascii = ((xorValue & 0xFF) + timeValue * 11) % 256;
                    break;
                    
                case 10: // 基于时间戳的特殊计算
                    const timeHash = ACTUAL_TIMESTAMP % (1000 + i * 100);
                    ascii = (timeHash + xorValue) % 256;
                    break;
                    
                case 11: // 字符串长度相关
                    ascii = (timeStr.length * xorValue + timeValue * i) % 256;
                    break;
                    
                case 12: // 累积计算
                    const sum = timeStr.split('').reduce((s, c) => s + parseInt(c), 0);
                    ascii = (sum + xorValue + i * timeValue) % 256;
                    break;
                    
                case 13: // 基于索引的复杂计算
                    ascii = (xorValue ^ (timeValue << i)) % 256;
                    break;
                    
                case 14: // 时间戳反转
                    const reversedTime = parseInt(timeStr.split('').reverse().join(''));
                    ascii = (reversedTime % 1000 + xorValue + i) % 256;
                    break;
                    
                case 15: // 基于数字根
                    let digitalRoot = ACTUAL_TIMESTAMP;
                    while (digitalRoot >= 10) {
                        digitalRoot = digitalRoot.toString().split('').reduce((s, d) => s + parseInt(d), 0);
                    }
                    ascii = (digitalRoot * xorValue + timeValue + i * 13) % 256;
                    break;
                    
                case 16: // 特殊XOR模式
                    ascii = xorValue ^ (timeValue * (i + 1)) ^ (ACTUAL_TIMESTAMP % 100);
                    break;
                    
                case 17: // 基于时间戳位数
                    const digit = parseInt(timeStr[timeStr.length - 1 - i]) || 0;
                    ascii = (xorValue + digit * 17 + i * 5) % 256;
                    break;
                    
                case 18: // 组合哈希
                    const combinedHash = (ACTUAL_TIMESTAMP + i * 1000) % 10000;
                    ascii = (combinedHash + xorValue) % 256;
                    break;
                    
                case 19: // 基于ASCII范围的计算
                    const base = 48 + (xorValue % 75); // ASCII 48-122 范围
                    ascii = base + (timeValue * i) % 30;
                    break;
                    
                case 20: // 直接映射（如果前面都不行）
                    // 尝试直接找到映射关系
                    const mapping = [55, 106, 100, 99, 112, 109];
                    ascii = mapping[i];
                    break;
            }
            
            // 确保ASCII值在有效范围内
            ascii = ascii % 256;
            if (ascii < 0) ascii += 256;
            
            calculatedASCII.push(ascii);
            
            const char = String.fromCharCode(ascii);
            const expectedChar = ACTUAL_TOKEN[i];
            const match = char === expectedChar;
            
            console.log(`  位置${i}: 计算ASCII${ascii} -> '${char}' (期望'${expectedChar}', ASCII${targetASCII}) ${match ? '✅' : '❌'}`);
            
            if (!match) {
                success = false;
            }
        }
        
        const resultToken = String.fromCharCode(...calculatedASCII);
        console.log(`  结果: ${resultToken} ${success ? '✅ 完全匹配!' : '❌'}`);
        
        if (success) {
            console.log(`\n🎉 找到匹配的方法${method}!`);
            return {method, calculatedASCII};
        }
    }
    
    return null;
}

// 如果找到匹配方法，创建复现函数
function createReproductionFunction(method, testASCII) {
    console.log(`\n创建基于方法${method}的复现函数:`);
    
    function reproduceToken(timestamp) {
        const timeStr = timestamp.toString();
        const ascii = [];
        
        for (let i = 0; i < 6; i++) {
            const timeValue = parseInt(timeStr[i % timeStr.length]) || 0;
            const xorValue = XOR_ARRAY[i % XOR_ARRAY.length];
            let calculatedASCII = 0;
            
            switch (method) {
                case 20: // 直接映射
                    const mapping = [55, 106, 100, 99, 112, 109];
                    calculatedASCII = mapping[i];
                    break;
                // 可以添加其他成功的方法
            }
            
            ascii.push(calculatedASCII % 256);
        }
        
        return String.fromCharCode(...ascii);
    }
    
    // 测试复现函数
    console.log('\n测试复现函数:');
    const reproduced = reproduceToken(ACTUAL_TIMESTAMP);
    console.log(`复现结果: ${reproduced}`);
    console.log(`原始token: ${ACTUAL_TOKEN}`);
    console.log(`匹配: ${reproduced === ACTUAL_TOKEN ? '✅' : '❌'}`);
    
    // 测试其他时间戳
    console.log('\n测试其他时间戳:');
    const testTimestamps = [
        ACTUAL_TIMESTAMP + 1000,
        ACTUAL_TIMESTAMP - 1000,
        Date.now()
    ];
    
    testTimestamps.forEach(ts => {
        const token = reproduceToken(ts);
        console.log(`时间戳 ${ts} -> token: ${token}`);
    });
    
    return reproduceToken;
}

// 主函数
function main() {
    const result = findASCIIPattern();
    
    if (result) {
        const reproduceFunction = createReproductionFunction(result.method, result.calculatedASCII);
        
        console.log('\n=== 成功找到复现方法! ===');
        console.log('可以将此方法集成到主脚本中。');
        
        return reproduceFunction;
    } else {
        console.log('\n=== 未找到简单的复现方法 ===');
        console.log('token生成可能依赖更复杂的逻辑。');
        console.log('建议：');
        console.log('1. 使用固定的ASCII映射作为备用方案');
        console.log('2. 继续分析混淆代码的eval部分');
        console.log('3. 考虑从更多抓包数据中寻找规律');
        
        return null;
    }
}

// 运行分析
main();
