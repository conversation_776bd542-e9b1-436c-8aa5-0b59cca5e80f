/**
 * 提取真实的token生成逻辑
 * 基于抓包文件中的完整混淆代码
 */

// 从抓包文件中提取的完整混淆代码
const fullObfuscatedCode = `/*qNVOMRa*/var/*RyR687XEI83CJb8fHR*/__qxBY2hC/*YMXkbo*/=\\u0053\\u0074\\u0072\\u0069\\u006e\\u0067
/*lXX1kbkRXJ9FmZqK*/./*MZCeD*/\\u0066r\\u006fm\\u0043ha\\u0072C\\u006fde/*sMmtyNpcAyql8O*/;
var/*Cfhx2UMXDK6BMZM*/_x_sEd = [/*dF4tvpacOzsnV9*/2212,53,2852,3445,3327,/*SPaJmEBdtdDh0xIQ5b*/];//vYnpXz6vIEdS5RW
var/*bcNoFOVIxVdidN*/_$3vge/*TLSE8sLsN11d*/=/*WloJg5inrhui0kzNK*/function(/*IzKb6MJKvAxCI*/){
/*4XMcxf8q6w5Eu9E8*/return/*tjsXXWm*/arguments[/*7KLqBRABhc6cioDsfg*/0]^/*NkiyNuHr*/
/*PX3neKSxLULiZ3uE*/_x_sEd[/*oZo7hG3hKugxQh6c5Sh*/0];/*Gy6Av9AnY5HTsFbp*/}/*QeINbNufqrnfY*/;
var/*v7zHapXuogfPBTAN*/_$89tQ/*UvaafX*/=/*3ns72qDDUBjuskBpx*/function(/*SBWW1CojExH8wpLEWg*/){
/*Rlccoq6ahe7EFcB4*/return/*WUb0GTgF*/arguments[/*P57JRWMXxW*/0]^/*wuGFWfO1*/
/*hrzhhgoyU*/_x_sEd[/*1eNOTd8mMjatIcEC*/1];/*QCKgolYTh*/}/*jKtj2G0Ls2TcH*/;
var/*SIFKkOuBNUnJlDrM*/_$2ez/*yfzoPOI8Fib00OELrm2*/=/*9w8TNqsIoLCi*/function(/*iN7pPA*/){
/*NRSvRnrefQJGwlSKF*/return/*hlpVATricR4QI*/arguments[/*TKhZPT*/0]^/*DcyqzBmfWtYZtrYq*/
/*ephneKlI5Klj3XaY*/_x_sEd[/*xQwiYQhHAIcVBl*/2];/*VaVwcP0KsBCb*/}/*O2AbLE3*/;
var/*EBCmqPRXJjw963BUW*/_$zcp/*FIxtMxvs7*/=/*hiQYBLvx4Q*/function(/*blmZnKgp9lNT*/){
/*OpFi3sgXXE4yRwWQtmg*/return/*UyuXTyv3kqhAm*/arguments[/*HOROTkaqm0n*/0]^/*YVuXf7JfY*/
/*dHvhIDpEqH*/_x_sEd[/*u3RnTzJIop*/3];/*dp2w9GwOVNCg6N*/}/*skQDPME88ahZdMrfaO*/;
var/*ZUvScLDkqPit2O*/_$mRym/*KPYaqzLzv1*/=/*RyJFl*/function(/*vj7hxeNNgVoNSQRv*/){
/*FpAZa7H*/return/*CLoUkWelj8eJ*/arguments[/*oQdReQyvmxXvuP*/0]^/*ExXATHEo*/
/*oUPGWDQRtI0aOZS*/_x_sEd[/*iqgXP7CzR0*/4];/*GPHHeemz7ZD54*/}/*jg0tDHN72hX*/;
/*0ZFQuMg3DmpJy7RR*/\\u0065\\u0076\\u0061\\u006c/*Lu7CM*/(__qxBY2hC(32)
+/*43ykoHOS7*/__qxBY2hC(1197/0xA,105,1104/0xA,Math.abs(100)&-1,0x6f07/0400,119,0x5b40/0400,39,100&(-1^0x00),~(0x66^/*qs*/-1),
49/*1G*/,49,57,54,0x3539/0400,~/*hHtav171ay9ta*/~/*d92RPvPGw*/52,39,93,61,0x2267>>/*TV30J1PDKYYmEjrNe*/4>>4,
108,_$mRym(Math.abs(3208)&-1),0x6332>>/*WuPAE5jp*/4>>4,_$2ez(2835),99,34,073,119/*0u*/,105,_$zcp(06433),
0x0|0x64,111,119,~/*1yJxYr2GRy*/~/*cjOqLXEDa89RG1UD6o*/91,_$zcp(3410),55/*pS*/,99,0x63,50&(-1^0x00),-1-~/*zx*/(0x64^0),
100,0x0|0x35,100,39,0x0|0x5d,~(0x3d^/*4W*/-1),0x2241/0400,118,57,118,
112,_$mRym(3277),0147,~/*QcguuHYWvr*/~/*HhTVoef*/105,0x7a,340/0xA,59,_$3vge(0x8d300>>/*mVdVLHDBau1p9C*/4>>4),~(0x69^/*lW*/-1),1100/0xA,
100,0x0|0x6f,0x7778/0400,0133,39,_$89tQ(0xd),100,97,~/*ZJBKeB6zv*/~/*hkOR6pEEGBVbeb*/52,100,
102&(-1^0x00),52,-1-~/*SkWT*/(0x61^0),0x2764/0400,93,61/*TB*/,34,101,0154,~/*rqvUpmDGeDFj*/~/*5I4srkW5*/49,
~/*Dm1SVynwBOjazUwXU*/~/*nc8ykMkMryQmTkSke*/56,0163,0x0|0x22,0x3b,119&(-1^0x00),0x0|0x69,1107/0xA,0x6457/0400,111,1191/0xA,
0x5b82/0400,39/*EC*/,52&(-1^0x00),57,48,97,~/*WfphZOr75cc*/~/*7dJVceQ*/55,0x32,~(0x66^/*iZq1*/-1),060)
+/*2esWT9z51NM2rBXhB3j*/__qxBY2hC(39&(-1^0x00),_$mRym(3234),_$3vge(~/*j5BSrIz5MPlPLwwltP5*/~/*NAmrUKYUYMiV*/2201),~(0x22^/*hLuS*/-1),0x7942/0400,~(0x6e^/*Z6x*/-1),0x0|0x79,104,_$2ez(Math.abs(2892)&-1),99,
0x22,59,119,105,0x6e15>>/*wyVkjQ*/4>>4,100/*aT*/,111,~/*HbEA9oQi1QmALfm4*/~/*hrKKYWxRQCZvH2Ptz*/119,~(0x5b^/*I0*/-1),39/*TL*/,
-1-~/*xgc*/(0x32^0),_$mRym(3275),49,54,067,~(0x37^/*LxSW*/-1),48,98,~(0x27^/*kUg*/-1),93,
_$89tQ(Math.abs(8)&-1),34,_$3vge(~(0x8c3^/*cQQ7*/-1)),119,0x6714/0400,119,106,~(0x22^/*K4gL*/-1),_$2ez(28475/0xA),119&(-1^0x00),
0151,0x6e,100,111/*by*/,Math.abs(119)&-1,0x5b63/0400,-1-~/*hvL5*/(0x27^0),51,0x3691>>/*LI30NNpdEGM*/4>>4,0x0|0x63,
~/*vLrvUfOJqFXd*/~/*neiUfA5NspTyvmb*/101,56/*XS*/,0145,0x30,102,39,93/*D3*/,0x3d68/0400,042,56,
122&(-1^0x00),~(0x6b^/*HRZ*/-1),0x6570>>/*ezLOY0gRNIwcJ*/4>>4,Math.abs(49)&-1,0x32,0x0|0x36,113,~(0x22^/*DJNK*/-1),59&(-1^0x00),119,
105&(-1^0x00),110,100,0x6f28>>/*dbxGocX4sAMZOM*/4>>4,~/*V69fCu*/~/*LyWLLzD*/119,0x5b77/0400,39&(-1^0x00),0145,57,Math.abs(48)&-1,
Math.abs(52)&-1,0143,_$3vge(Math.abs(2193)&-1),57,Math.abs(100)&-1,0x2751/0400,0x5d21>>/*fTfUS7WNG87*/4>>4,61,34/*sB*/,Math.abs(112)&-1,
0x0|0x74,56,~/*OITnFDnOxcukpiA*/~/*Icn5XDqkESbArVs*/100,-1-~/*iP*/(0x63^0),110,~/*OqVG2EXk5G1v9TFEd*/~/*DY60IPak61Up6s2Yf*/121,0x2250>>/*TMgQxRDH6FtoQuFg*/4>>4,_$3vge(-1-~/*xZ2*/(0x89f^0)),0x0|0x77,105)
+/*Ld2RtXwbAOOJrEorkI*/__qxBY2hC(0x0|0x6e,0x6463/0400,~(0x6f^/*U9*/-1),0x0|0x77,91&(-1^0x00),39,_$mRym(~/*a5eV3d*/~/*ZnldfhGWepvc*/3272),100&(-1^0x00),0141,0x0|0x61,
0x3694>>/*3ULJGrsRAgty*/4>>4,481/0xA,~/*svdpy35c08ypra*/~/*bBSGrnKp*/50,0x35,0x27,0x5d79>>/*E8Jhv*/4>>4,~(0x3d^/*D2*/-1),0x0|0x22,115,~(0x7a^/*Zxvw*/-1),
1212/0xA,-1-~/*Eo0x*/(0x39^0),118,-1-~/*w69*/(0x37^0),~(0x33^/*uDD*/-1),112,34,0x0|0x3b,Math.abs(119)&-1,0x6949/0400,
1102/0xA,100,0x0|0x6f,-1-~/*kjn*/(0x77^0),91,~(0x27^/*pda*/-1),0x30,0x3165/0400,101,1001/0xA,
_$89tQ(865/0xA),_$mRym(3229),51/*xd*/,116,39,0x5d99>>/*GIC6vk7tvCUO01Kz*/4>>4,~(0x3d^/*lKvR*/-1),34&(-1^0x00),55,106,
-1-~/*xF*/(0x64^0),0x63,112,1096/0xA,34,59,Math.abs(119)&-1,0x69,110,0x6450>>/*S1Af6rlgIF2p*/4>>4,
Math.abs(111)&-1,-1-~/*rTt*/(0x77^0),91&(-1^0x00),39,-1-~/*xYHk*/(0x61^0),54,0141,0x0|0x34,52,48,
102,~(0x31^/*ZE0*/-1),0x2724/0400,93/*n0*/,Math.abs(61)&-1,0x0|0x22,54/*qo*/,0x7393>>/*LEfUnhtDkSXF*/4>>4,54,0x3129/0400,
~(0x32^/*TLR*/-1),118,112,-1-~/*xRmJ*/(0x35^0),100&(-1^0x00),0x22,0x0|0x3b,~/*u1kX1rQF6kY*/~/*O6dT8e*/119,105,110/*B9*/,
0x6494/0400,111,119,Math.abs(91)&-1,0x27,~(0x65^/*Uwpb*/-1),99/*5o*/,97/*TE*/,_$2ez(2845),56/*bf*/)`;

console.log('=== 提取真实token生成逻辑 ===\n');

// 抓包数据
const ACTUAL_TIMESTAMP = 1754383264519;
const ACTUAL_TOKEN = '7jdcpm';

console.log(`目标时间戳: ${ACTUAL_TIMESTAMP}`);
console.log(`目标token: ${ACTUAL_TOKEN}\n`);

// 尝试执行完整的混淆代码
function executeFullObfuscatedCode() {
    try {
        console.log('1. 尝试执行完整的混淆代码...');
        
        const vm = require('vm');
        let capturedToken = null;
        let allOutputs = [];
        
        // 创建执行环境
        const context = {
            String: String,
            Math: Math,
            parseInt: parseInt,
            parseFloat: parseFloat,
            console: {
                log: function(...args) {
                    allOutputs.push(args);
                    console.log('VM输出:', ...args);
                    args.forEach(arg => {
                        if (typeof arg === 'string' && /^[a-z0-9]{6,12}$/.test(arg)) {
                            capturedToken = arg;
                            console.log(`🎯 捕获到可能的token: ${arg}`);
                        }
                    });
                }
            },
            eval: function(code) {
                try {
                    console.log('执行eval代码...');
                    const result = vm.runInContext(code, context, { timeout: 10000 });
                    console.log(`eval结果类型: ${typeof result}, 值: ${result}`);
                    
                    if (typeof result === 'string' && /^[a-z0-9]{6,12}$/.test(result)) {
                        capturedToken = result;
                        console.log(`🎯 从eval结果捕获token: ${result}`);
                    }
                    return result;
                } catch (e) {
                    console.log(`eval执行失败: ${e.message}`);
                    return undefined;
                }
            }
        };

        vm.createContext(context);
        
        console.log('开始执行混淆代码...');
        const result = vm.runInContext(fullObfuscatedCode, context, {
            timeout: 30000,
            displayErrors: true
        });

        console.log(`\n执行结果:`);
        console.log(`- 类型: ${typeof result}`);
        console.log(`- 值: ${result}`);
        console.log(`- 捕获的token: ${capturedToken}`);
        console.log(`- 所有输出: ${allOutputs.length}个`);
        
        return capturedToken;
        
    } catch (error) {
        console.log(`执行失败: ${error.message}`);
        console.log(`错误堆栈: ${error.stack}`);
        return null;
    }
}

// 尝试手动解析eval中的代码
function manualParseEvalCode() {
    try {
        console.log('\n2. 尝试手动解析eval中的代码...');
        
        // 提取eval调用
        const evalMatch = fullObfuscatedCode.match(/\\u0065\\u0076\\u0061\\u006c.*?__qxBY2hC\([^)]+\)/);
        if (evalMatch) {
            console.log('找到eval调用');
            
            // 解码Unicode
            let evalCode = evalMatch[0];
            evalCode = evalCode.replace(/\\u([0-9a-fA-F]{4})/g, (match, hex) => {
                return String.fromCharCode(parseInt(hex, 16));
            });
            
            console.log(`解码后的eval调用: ${evalCode.substring(0, 100)}...`);
        }
        
        // 尝试构建执行环境并手动计算
        const _x_sEd = [2212, 53, 2852, 3445, 3327];
        const __qxBY2hC = String.fromCharCode;
        
        const _$3vge = function(x) { return x ^ _x_sEd[0]; };
        const _$89tQ = function(x) { return x ^ _x_sEd[1]; };
        const _$2ez = function(x) { return x ^ _x_sEd[2]; };
        const _$zcp = function(x) { return x ^ _x_sEd[3]; };
        const _$mRym = function(x) { return x ^ _x_sEd[4]; };
        
        console.log('构建的函数环境:');
        console.log(`_$3vge(100) = ${_$3vge(100)}`);
        console.log(`_$89tQ(100) = ${_$89tQ(100)}`);
        console.log(`_$2ez(100) = ${_$2ez(100)}`);
        console.log(`_$zcp(100) = ${_$zcp(100)}`);
        console.log(`_$mRym(100) = ${_$mRym(100)}`);
        
        // 尝试计算一些关键值
        console.log('\n计算一些关键字符:');
        console.log(`__qxBY2hC(32) = "${__qxBY2hC(32)}"`);
        console.log(`__qxBY2hC(119, 105, 110, 100, 111, 119) = "${__qxBY2hC(119, 105, 110, 100, 111, 119)}"`);
        
        return null;
        
    } catch (error) {
        console.log(`手动解析失败: ${error.message}`);
        return null;
    }
}

// 主函数
function main() {
    const token1 = executeFullObfuscatedCode();
    const token2 = manualParseEvalCode();
    
    console.log('\n=== 结果对比 ===');
    console.log(`目标token: ${ACTUAL_TOKEN}`);
    console.log(`执行结果: ${token1 || 'null'} ${token1 === ACTUAL_TOKEN ? '✅' : '❌'}`);
    console.log(`手动解析: ${token2 || 'null'} ${token2 === ACTUAL_TOKEN ? '✅' : '❌'}`);
    
    if (token1 === ACTUAL_TOKEN) {
        console.log('\n🎉 成功复现token！');
        return token1;
    } else {
        console.log('\n❌ 仍未能复现token');
        return null;
    }
}

// 运行测试
main();
