#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试顺丰登录功能
"""

import os
import sys

# 设置环境变量
os.environ['sfsyUrl'] = 'https://mcs-mimp-web.sf-express.com/up-member/newHome?mobile=181****5357&userId=73FE37E7FC644FF5BD7709C54B895007&path=/up-member/newHome&supportShare=NO&maskType=autoReceive&from=surprise_benefitwxauto&equityKey=surprise_benefit&citycode=833&cityname=%E4%B9%90%E5%B1%B1'

# 导入脚本
try:
    # 重命名导入以避免文件名中的点号问题
    import importlib.util
    spec = importlib.util.spec_from_file_location("sf_script", "顺丰1.1.py")
    sf_script = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(sf_script)
    
    print("=" * 50)
    print("测试顺丰登录功能")
    print("=" * 50)
    
    # 创建RUN实例并测试登录
    url = os.environ['sfsyUrl']
    print(f"测试URL: {url[:100]}...")
    
    run_instance = sf_script.RUN(url, 0)
    
    if run_instance.login_res:
        print("✅ 登录测试成功!")
        print(f"用户ID: {run_instance.user_id}")
        print(f"手机号: {run_instance.mobile}")
    else:
        print("❌ 登录测试失败")
        
except Exception as e:
    print(f"❌ 测试异常: {str(e)}")
    import traceback
    traceback.print_exc()
