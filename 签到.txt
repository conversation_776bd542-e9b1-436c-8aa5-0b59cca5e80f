# 创维会员中心签到逻辑分析

## 完整签到流程

### 1. 获取当前积分
**接口**: `POST /ctool/getCredits?_={timestamp}`
**参数**: 
- `_`: 当前时间戳（防缓存）
**响应**: 返回当前积分数量
**Cookie要求**: 需要完整的用户认证Cookie

### 2. 获取签到页面信息
**接口**: `GET /sign/component/index?signOperatingId=303855131763271&preview=false&_={timestamp}`
**参数**:
- `signOperatingId`: 固定值 `303855131763271` (签到活动ID)
- `preview`: 固定值 `false`
- `_`: 当前时间戳（防缓存）
**响应**: 返回签到日历状态，确认当日是否可签到
**关键字段**: `calendarInfo.dateInfos[].signStatus` (6=可签到)

### 3. 获取安全Token
**接口**: `POST /chw/ctoken/getToken`
**请求体**: `timestamp={current_timestamp}`
**参数来源**: 
- `timestamp`: 当前时间戳毫秒数
**响应**: 返回复杂的混淆JavaScript代码
**处理逻辑**: 客户端需执行JavaScript代码生成简化token

### 4. 执行签到 (核心接口)
**接口**: `POST /sign/component/doSign?_={timestamp}`
**请求体**: `signOperatingId=303855131763271&token={parsed_token}`
**参数来源**:
- `signOperatingId`: 从步骤2获取，固定值 `303855131763271`
- `token`: 从步骤3的JavaScript代码执行后获得，如 `gymf74nl`
- `_`: 当前时间戳（防缓存）
**响应**: 
```json
{
  "success": true,
  "data": {
    "signResult": 100,
    "orderNum": "402844888"
  }
}
```

### 5. 查询签到结果
**接口**: `GET /sign/component/signResult?orderNum={order_num}&_={timestamp}`
**参数来源**:
- `orderNum`: 从步骤4响应中获取，如 `402844888`
- `_`: 当前时间戳（防缓存）
**响应**:
```json
{
  "success": true,
  "data": {
    "signResult": 2,
    "credits": 5,
    "orderNum": "402844888"
  }
}
```

### 6. 获取更新后积分
**接口**: `POST /ctool/getCredits?_={timestamp}`
**参数**: 同步骤1
**响应**: 返回签到后的积分数量（原积分+5维豆）

## 关键参数传递规则

### Cookie要求 (所有接口必需)
```
wdata4: 加密用户数据
w_ts: 时间戳
_ac: {"aid":74367,"cid":4306181526} (Base64编码)
tokenId: 用户token标识
wdata3: 加密数据字段
createdAtToday: false
isNotLoginUser: false
dcustom: 用户信息(头像+昵称，URL编码)
```

### Headers要求
```
User-Agent: 微信小程序环境标识
Content-Type: application/x-www-form-urlencoded (POST请求)
Accept: application/json, text/plain, */*
Origin: https://74367-1-activity.m.dexfu.cn
Referer: https://74367-1-activity.m.dexfu.cn/sign/component/page?signOperatingId=303855131763271&from=login&spm=74367.1.1.1
```

### 时间戳规则
- 所有接口的 `_` 参数使用当前时间戳毫秒数
- 获取token接口的 `timestamp` 参数使用当前时间戳毫秒数
- 时间戳用于防重放攻击和缓存控制

### Token解析规则
1. 获取混淆JavaScript代码
2. 客户端执行JavaScript代码
3. 代码执行后生成简化token (如: gymf74nl)
4. 将简化token用于签到接口

### 错误处理
- 如果token无效，签到接口返回错误
- 如果已签到，返回相应状态码
- 通过orderNum查询最终签到结果确认成功

## 参数类型详细分析

### 动态参数 (每次请求都变化)
1. **时间戳参数**
   - `_`: URL参数，当前时间戳毫秒数，用于防缓存
   - `timestamp`: POST请求体参数，当前时间戳毫秒数，用于token生成
   - **来源**: `Date.now()` 客户端生成

2. **动态Token**
   - `token`: 签到接口核心参数，如 `gymf74nl`
   - **来源**: 从 `/chw/ctoken/getToken` 接口获取JavaScript代码执行后生成
   - **生成过程**:
     - 请求获取混淆JavaScript代码
     - 客户端执行JavaScript代码
     - 代码执行后返回简化token字符串

3. **订单号**
   - `orderNum`: 签到后生成的订单号，如 `402844888`
   - **来源**: 签到接口 `/sign/component/doSign` 响应中获取
   - **用途**: 用于查询签到结果

### 签名参数 (用于身份验证和安全校验)
1. **自动登录签名**
   - `sign`: 如 `f9317128b95096706b8ee4c6152da567`
   - **来源**: 自动登录接口 `/autoLogin/autologin` 的URL参数
   - **用途**: 验证登录请求的合法性

2. **微信JS-SDK签名**
   - `signature`: 如 `383dacbfcf4669c1562eedaa0db44613b5cab66b`
   - `nonceStr`: 如 `647e3098-f5c8-43ff-b7f0-95a8f384f5ce`
   - **来源**: 页面HTML中的wx.config配置
   - **用途**: 微信JS-SDK身份验证

### 固定参数 (不变的配置参数)
1. **应用配置**
   - `appId`: 固定值 `74367`
   - `signOperatingId`: 固定值 `303855131763271` (签到活动ID)
   - `appKey`: 固定值 `4KXh6ZM4FM8JuSBdanaBi61wK9w7`
   - **来源**: 页面HTML中的CFG配置对象

2. **微信配置**
   - `appId` (微信): 固定值 `wx0f69ba355f7210fd`
   - **来源**: 页面HTML中的wx.config配置

### 用户身份参数 (登录后固定，但不同用户不同)
1. **用户标识**
   - `uid`: 如 `1ab88814cc48f77f`
   - `credits`: 当前积分数，如 `305`
   - **来源**: 自动登录接口URL参数

2. **JWT Token**
   - `Authorization`: Bearer token，包含用户身份信息
   - **来源**: 微信小程序登录后获取
   - **示例**: `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...`

## 动态参数获取来源接口

### 0. JWT Token获取流程 (前置步骤)
**第一步 - 换取ticket**:
**接口**: `POST /miniprogram/api/v2/user/exchange`
**域名**: `uc-api.skyallhere.com`
**请求体**: `{"code": "{微信授权码}"}`
**响应**: `{"code":0,"data":{"ticket":"f3351ccad465bd5c977d631d564afa50"},"msg":"ok"}`

**第二步 - 获取JWT Token**:
**接口**: `POST /miniprogram/api/v2/user/signin`
**域名**: `uc-api.skyallhere.com`
**请求体**: `{"ticket": "{上一步获取的ticket}"}`
**响应**: `{"code":0,"data":{"token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."},"msg":"ok"}`
**用途**: 获取后续所有接口需要的JWT Token

### 1. 用户身份获取
**接口**: `GET /miniprogram/api/v1/get-user`
**域名**: `uc-api.skyallhere.com`
**Headers**: `Authorization: Bearer {JWT_Token}`
**用途**: 获取用户基础信息和积分
**关键响应字段**:
- `userScore`: 当前积分
- `inviteTag`: 用户标识码
- `nickName`: 用户昵称

### 2. 自动登录
**接口**: `GET /autoLogin/autologin`
**参数来源**:
- `sign`: 服务端生成的签名
- `uid`: 用户唯一标识
- `credits`: 当前积分
- `timestamp`: 当前时间戳
- `dcustom`: 用户自定义信息(头像+昵称)
**响应**: 设置所有必需的Cookie

### 3. 页面配置获取
**接口**: `GET /sign/component/page`
**响应**: HTML页面包含CFG配置对象
**关键配置**:
```javascript
var CFG = {
    appId: 74367,
    signOperatingId: 303855131763271,
    appKey: '4KXh6ZM4FM8JuSBdanaBi61wK9w7'
}
```

### 4. 动态Token生成
**接口**: `POST /chw/ctoken/getToken`
**输入**: `timestamp={current_timestamp}`
**输出**: 混淆JavaScript代码
**处理**: 客户端执行JavaScript生成token

## 安全机制
1. **JavaScript混淆**: 防止直接调用签到接口
2. **时间戳验证**: 防重放攻击
3. **多层Cookie验证**: 确保用户身份
4. **订单机制**: 异步处理签到结果
5. **User-Agent检查**: 限制微信小程序环境
6. **签名验证**: 自动登录和微信JS-SDK双重签名验证
