# -*- coding: utf-8 -*-
import requests
from urllib.parse import unquote
import json

def test_sf_url():
    test_url = "https://mcs-mimp-web.sf-express.com/mcs-mimp/share/weChat/activityRedirect?source=CX&unionId=YQ1N3lUDNaSg9Ox9%2BBwNXRFFsW2OBAKilgqhG8pgOBo%3D&openId=s1G5ik6AWFIQQaL5sshp%2FZwTIdtITonvNlva7jmu380%3D&memId=hych5uZiPmTZK%2FpoxrKOmetUH%2BV8uGn%2F%2FYU88EDDho4DdjSr%2F6X0WoiZtgGzs7sG&memNo=6tB15T6k0ZvyroDMcHJm6TxGNOheCN75HuG7rOc5Fw4DdjSr%2F6X0WoiZtgGzs7sG&mobile=EpJ84tf34s%2B0ot4Y2kXwuA%3D%3D&mediaCode=wxapp&bizCode=%7B%22path%22%3A%22%2Fup-member%2FnewHome%22%2C%22supportShare%22%3A%22NO%22%2C%22maskType%22%3A%22autoReceive%22%2C%22from%22%3A%22surprise_benefitwxauto%22%2C%22equityKey%22%3A%22surprise_benefit%22%7D&citycode=833&cityname=%E4%B9%90%E5%B1%B1&cx-at-sign=C6E170041AF7782DC61DDEDD46E8C24CA3A0D1D4AD24CD5D0505763F8F4E0FA7&cx-at-ts=1754409196&cx-at-nonce=qgAzD0Sotk60w19Okw3CV&t=1754409196"
    
    print("🔍 测试顺丰URL...")
    print(f"📋 原始URL长度: {len(test_url)}")
    
    # URL解码
    decoded_url = unquote(test_url)
    print(f"📋 解码后URL长度: {len(decoded_url)}")
    print(f"🌐 解码后URL: {decoded_url[:100]}...")
    
    # 模拟请求头
    headers = {
        'Host': 'mcs-mimp-web.sf-express.com',
        'upgrade-insecure-requests': '1',
        'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/98.0.4758.102 Safari/537.36 NetType/WIFI MicroMessenger/7.0.20.1781(0x6700143B) WindowsWechat(0x63090551) XWEB/6945 Flue',
        'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9',
        'sec-fetch-site': 'none',
        'sec-fetch-mode': 'navigate',
        'sec-fetch-user': '?1',
        'sec-fetch-dest': 'document',
        'accept-language': 'zh-CN,zh',
        'platform': 'MINI_PROGRAM',
    }
    
    try:
        print("🚀 发起请求...")
        session = requests.Session()
        session.verify = False
        
        response = session.get(decoded_url, headers=headers, timeout=30)
        print(f"📊 响应状态码: {response.status_code}")
        print(f"📊 响应头: {dict(response.headers)}")
        
        # 检查cookies
        cookies = session.cookies.get_dict()
        print(f"🍪 获取到的Cookies: {cookies}")
        
        user_id = cookies.get('_login_user_id_', '')
        phone = cookies.get('_login_mobile_', '')
        
        if phone:
            mobile = phone[:3] + "*" * 4 + phone[7:] if phone else ''
            print(f"✅ 登录成功!")
            print(f"👤 用户ID: {user_id}")
            print(f"📱 手机号: {mobile}")
        else:
            print("❌ 未获取到用户信息")
            print(f"📄 响应内容前500字符: {response.text[:500]}")
            
    except Exception as e:
        print(f"❌ 请求异常: {str(e)}")

if __name__ == '__main__':
    test_sf_url()
