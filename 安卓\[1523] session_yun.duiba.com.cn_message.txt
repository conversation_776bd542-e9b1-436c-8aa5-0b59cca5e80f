GET /h5/sign_custom/skins/normal-calendar-sign_190826/component-modal.21536387.css h2
host: yun.duiba.com.cn
sec-ch-ua-platform: "Android"
user-agent: Mozilla/5.0 (Linux; Android 13; M2012K11AC Build/TKQ1.221114.001; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/138.0.7204.180 Mobile Safari/537.36 XWEB/1380085 MMWEBSDK/20240404 MMWEBID/4824 MicroMessenger/8.0.49.2600(0x28003133) WeChat/arm64 Weixin NetType/WIFI Language/zh_CN ABI/arm64 miniProgram/wxff438d3c60c63fb6
sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Android WebView";v="138"
sec-ch-ua-mobile: ?1
accept: text/css,*/*;q=0.1
x-requested-with: com.tencent.mm
sec-fetch-site: cross-site
sec-fetch-mode: no-cors
sec-fetch-dest: style
sec-fetch-storage-access: active
referer: https://74367-1-activity.m.dexfu.cn/
accept-encoding: gzip, deflate, br, zstd
accept-language: zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7
priority: u=0



h2 200
last-modified: Fri, 07 Jan 2022 09:05:38 GMT
content-encoding: gzip
server: AliyunOSS
date: Sun, 03 Aug 2025 09:08:08 GMT
content-type: text/css
vary: Accept-Encoding
x-oss-request-id: 688F26F8F21E4B33313E8D57
x-oss-object-type: Normal
x-oss-hash-crc64ecma: 7962989132127091017
x-oss-storage-class: Standard
content-md5: TD+T7MbCq90jIDZw+PqtNw==
x-oss-server-time: 36
content-length: 3967
accept-ranges: bytes
x-nws-log-uuid: 1875556941237340878
x-cache-lookup: Cache Hit
access-control-allow-origin: *
access-control-expose-headers: Content-Length,Range
access-control-allow-methods: GET,HEAD,OPTIONS
access-control-allow-headers: Content-Length,Range
cache-control: max-age=31536000

.normal-page[data-v-b009b054],
.normal-page-content[data-v-b009b054] {
  font-size: 0.32rem;
  width: 100%;
  background-color: #fff;
  position: relative;
}
.opacity-4[data-v-b009b054] {
  opacity: 0.4;
}
.blur[data-v-b009b054] {
  -webkit-filter: blur(0.066667rem);
  filter: blur(0.066667rem);
}
.icon[data-v-b009b054] {
  width: 1em;
  height: 1em;
  vertical-align: -0.15em;
  fill: currentColor;
  overflow: hidden;
}
.scale-enter-active[data-v-b009b054] {
  -webkit-transition: opacity 0.3s, -webkit-transform 0.3s;
  transition: opacity 0.3s, -webkit-transform 0.3s;
  -o-transition: opacity 0.3s, transform 0.3s;
  transition: opacity 0.3s, transform 0.3s;
  transition: opacity 0.3s, transform 0.3s, -webkit-transform 0.3s;
}
.scale-enter-active > div[data-v-b009b054]:nth-of-type(1),
.scale-enter-active > div[data-v-b009b054]:nth-of-type(2),
.scale-enter-active > section[data-v-b009b054]:first-child {
  -webkit-animation: bounceIn-data-v-b009b054 0.3s linear alternate both;
  animation: bounceIn-data-v-b009b054 0.3s linear alternate both;
}
.scale-leave-active[data-v-b009b054] {
  -webkit-transition: opacity 0.2s, -webkit-transform 0.2s;
  transition: opacity 0.2s, -webkit-transform 0.2s;
  -o-transition: opacity 0.2s, transform 0.2s;
  transition: opacity 0.2s, transform 0.2s;
  transition: opacity 0.2s, transform 0.2s, -webkit-transform 0.2s;
}
.scale-leave-active > div[data-v-b009b054]:nth-of-type(1),
.scale-leave-active > div[data-v-b009b054]:nth-of-type(2),
.scale-leave-active > section[data-v-b009b054]:first-child {
  -webkit-animation: bounceOut-data-v-b009b054 0.2s linear alternate both;
  animation: bounceOut-data-v-b009b054 0.2s linear alternate both;
}
.scale-leave-to[data-v-b009b054] {
  opacity: 0;
}
.scale-leave-to > div[data-v-b009b054]:nth-of-type(1),
.scale-leave-to > div[data-v-b009b054]:nth-of-type(2),
.scale-leave-to > section[data-v-b009b054]:first-child {
  opacity: 0;
}
@-webkit-keyframes bounceIn-data-v-b009b054 {
0% {
    opacity: 0;
    -webkit-transform: scale(0.5);
    transform: scale(0.5);
}
100% {
    opacity: 1;
    -webkit-transform: scale(1);
    transform: scale(1);
}
}
@keyframes bounceIn-data-v-b009b054 {
0% {
    opacity: 0;
    -webkit-transform: scale(0.5);
    transform: scale(0.5);
}
100% {
    opacity: 1;
    -webkit-transform: scale(1);
    transform: scale(1);
}
}
@-webkit-keyframes bounceOut-data-v-b009b054 {
0% {
    opacity: 1;
    -webkit-transform: scale(1);
    transform: scale(1);
}
100% {
    opacity: 0;
    -webkit-transform: scale(0.5);
    transform: scale(0.5);
}
}
@keyframes bounceOut-data-v-b009b054 {
0% {
    opacity: 1;
    -webkit-transform: scale(1);
    transform: scale(1);
}
100% {
    opacity: 0;
    -webkit-transform: scale(0.5);
    transform: scale(0.5);
}
}
.modal[data-v-b009b054] {
  width: 100%;
  height: 100%;
  position: fixed;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  z-index: 1001;
  outline: 0;
  overflow: hidden;
  background: rgba(0, 0, 0, 0.55);
}
.modal .modal-body[data-v-b009b054] {
  position: absolute;
  width: 8.533333rem;
  height: auto;
  top: 50%;
  right: calc(50% - 4.266667rem);
  bottom: auto;
  left: auto;
  margin-top: -6.173333rem;
  border-radius: 0.266667rem;
  background: #fff;
  z-index: 1002;
}
.modal .modal-body .close-icon[data-v-b009b054] {
  width: 0.666667rem;
  height: 0.666667rem;
  position: absolute;
  right: 0.4rem;
  top: 0.4rem;
}
.modal .modal-body .close[data-v-b009b054] {
  position: absolute;
  width: 0.906667rem;
  height: 0.906667rem;
  top: auto;
  right: calc(50% - 0.453333rem);
  bottom: -2.08rem;
  left: auto;
  background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAIgAAACICAMAAAALZFNgAAAAjVBMVEUAAAD29vb29vb////29vb19fX19fX////29vb4+Pj19fX29vb////19fX29vb29vb29vb29vb39/f5+fn8/Pz19fX39/f39/f19fX29vb////29vb29vb29vb19fX39/f39/f29vb4+Pj19fX5+fn29vb29vb29vb29vb19fX29vb19fX29vb39/f19fWhbwEeAAAALnRSTlMA8TcU+tNnBRwh7JIBm/bfzcJMKRm1WzyDUQ/GjYmfd2GpRegtrePhpLlxaVY/t5Gn9QAABTRJREFUeNrUl+1yqjAQhl8EGj5FEcHvqq09ttXc/+WdGQLs0ZpqWHDmPL/8IfBM9t1Ngv+f7Pyy2n0u3SRwnCBxl5+71cs5w1Pxt5uRJW9ijTZbH88gy+NE3iGJ8wy9Yn8v5YMsv230hJgfHGmAc5gLdI+9DqQxwdpGt3jhUF4xHO1m+b54920hbP+92Oez3ejnv0IP3eHHVzUZrVIvwg0iL12NrioU++iGRXihkYRz+04V52FyoRIu0AH5mySsuIjwAFERW5J4y8FlMpbE9CQMmuw0lcR4Ag5iRlVxjsbvmhz/eXwmGL0yvogcM+ZjDy1JaXJsWr/E29BUSdEGsZY1bgEGhStr1gLGLJqyWF8CLMSX1ZTHuJE9t2mVDGyypoFczzDvf+qQvkbogOi1Du0fo94b1DH9GKAjio86sgav3A/rstjoDLsuz3D/8HrUHmGEDonC2mTwYD7quryiY17r6jyUE6/KqbNF52ydKrEe7rJwq/VL0QNpVXV3gTuIcbUeKXohdarJJvA79Vzfoie29bS/I2yQU2Zi01+DGlR9ix4Jq9bx7gdkGqFHoundmMyquW6jV+xq2s+gYaIS7RTomaL6kG6ujc2Cyg/sGDfJTQPCj0mOGyzU/cXK8AQyS913Fvqu+sJT+NLOCV8FyBV4CsJVefVxTawUCzyJQpbEuMJTC7LR3/mS4OAbXt4/gqM+cRu1JN7NhDiezmNZBnlgMrbcMo5aec+5lRJ7SAulmblkYuChmxUUhqF9Y/d3tPqJNDJRHgoPGnzn53lAqF33CB2BJBMzD/kOHUe1CwsQc1kygY6DJBMzjzcBHRNZMv/xnSm0+JaBCXmoz2hRg/4AelBV6wQ9A43JPY8X/MJJJZPi+q0+IGBiwveAUO/8Rs2SetfAhOlBHbxERUbT3cCE7UFzPrs4iCQRzEz4HoiSi2NJTLPWwIThQYQXoUioz0xMuB40wBKU+LLEhqkJ3wO2LPFBl8ARwDAx9CBG1QWXDgYrtDBhe2BVHYNIKkUbE64HUioHLNqtzU2YHvDUe2icDSO0M+F5IBo2I+1Mi9PKhOFBwTgDeCl/7cAwYXhgpx5qcjsDw4ThgVnTswca9y1NUoYH8qYgn+WvPdqbSIYH9uVzn81hZACGCcMDg+ZI4tJZ29iE74F3deVu9l4fDBOGB/xm/w1o7zU34XnQ/hsAUCd4gTakklihFUKd5BkiNMdosjFEqDQsD2XCKA2FleFBJoywUvsyPMiE075qoBUMD5ZJoQYajXiGx/ovs3aMgyAQRAEUiQ2RAoONdgoRq73/8eycbPUy2ZgsNcWrYOb/OTVI4hP/1E/P+8tIiX96HgPsGCzxGODByA5LPBh5VLSjTRKjoodnOyzx8Ox1wg5LvE7EgtXmsMQLlldOOyzxyukl3A5LvIRHLNHisMSxhIMaOyxxUOPoyg5LHF05zLPDEod5jjftsMTxpgNfOyxx4OsI3A5LHIEnSoFH7UhI9kwp4JrkUjkyksU1SaY4eleOlGRicZSr0sKRlKys0jLl4iccSckxq1xM1a23LRwpyfnFupUFdM2/lrLdc6vTUsoRDhTQrOTjjT29qE/rPLCS7/VIoZ+zjW4OWfo57enm2Kmf86/fQdw4/PkZv93csQkAAAgDQXD/oa3SaiUcZo7c5xCnXwSd0yRzI3WOtc7V2DlfM3d056DvkAUHcTisxYE+Dn1yMJjD4xww6BBKCJU6zNaBxxDFhnC6w/XHgEElYFBbwOBZ0gGKXEDZDymEIqVhqFjOxRokyljXFHXtfAAAAABJRU5ErkJggg==);
  background-size: 100% 100%;
  background-repeat: no-repeat;
  z-index: 1003;
}
[duiba-webp='true'] .modal .modal-body .close[data-v-b009b054] {
  background-image: url(data:image/png;base64,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);
  background-size: 100% 100%;
  background-repeat: no-repeat;
}
.modal .modal-body .popup-bg-image[data-v-b009b054] {
  position: absolute;
  width: auto;
  height: auto;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background-size: cover;
}
.modal .modal-body .decration-before[data-v-b009b054] {
  position: absolute;
  width: 2.666667rem;
  height: 2.666667rem;
  top: -0.4rem;
  right: auto;
  bottom: auto;
  left: -0.8rem;
  border-radius: 2rem;
  z-index: -1;
}
.modal .modal-body .decration-after[data-v-b009b054] {
  position: absolute;
  width: 3.333333rem;
  height: 3.333333rem;
  top: 3.56rem;
  right: -0.8rem;
  bottom: auto;
  left: auto;
  border-radius: 2.333333rem;
  z-index: -1;
}
.modal .modal-body .rule-body[data-v-b009b054] {
  padding: 0.64rem 0.88rem 1.04rem;
  color: #192962;
}
.modal .modal-body .rule-body .header[data-v-b009b054] {
  text-align: center;
  height: 0.706667rem;
  font-size: 0.506667rem;
  font-family: PingFangSC;
  font-weight: 600;
  color: #333333;
  line-height: 0.706667rem;
  margin-bottom: 0.226667rem;
}
.modal .modal-body .rule-body .wrapper[data-v-b009b054] {
  height: 7.666667rem;
  overflow-y: scroll;
  -webkit-overflow-scrolling: touch;
  overflow-x: hidden;
  font-size: 0.373333rem;
  font-family: PingFangSC;
  font-weight: 400;
  color: #666666;
  line-height: 0.533333rem;
}
.modal .modal-body .rule-body .wrapper[data-v-b009b054] ol {
  list-style-type: decimal;
  padding-left: 0.533333rem;
}
.modal .modal-body .rule-body .wrapper[data-v-b009b054] ul {
  list-style-type: disc;
  padding-left: 0.533333rem;
}
.modal .modal-body .rule-body .wrapper[data-v-b009b054] em {
  font-style: italic;
}
.modal .modal-body.thanks[data-v-b009b054] {
  margin-top: -5.04rem;
}
.modal .modal-body.thanks .thanks-body[data-v-b009b054] {
  padding: 0.533333rem 0 0;
  height: 8rem;
  position: relative;
  text-align: center;
}
.modal .modal-body.thanks .thanks-body .header[data-v-b009b054] {
  height: 0.706667rem;
  font-size: 0.506667rem;
  font-family: PingFangSC;
  font-weight: 600;
  color: #333333;
  line-height: 0.706667rem;
}
.modal .modal-body.thanks .thanks-body .thanks-tip[data-v-b009b054] {
  margin-top: 0.32rem;
  height: 0.533333rem;
  font-size: 0.373333rem;
  font-family: PingFangSC;
  font-weight: 400;
  color: #666666;
  line-height: 0.533333rem;
}
.modal .modal-body.thanks .thanks-body .thanks-img[data-v-b009b054] {
  width: 4.906667rem;
  height: 2.546667rem;
  margin: 0.76rem auto 0.72rem;
  background: url(//yun.duiba.com.cn/h5/sign_custom/skins/normal-calendar-sign_190826/image/modal-thanks-img.2f89240f.png);
  background-size: 100% 100%;
  background-repeat: no-repeat;
}
[duiba-webp='true'] .modal .modal-body.thanks .thanks-body .thanks-img[data-v-b009b054] {
  background-image: url(//yun.duiba.com.cn/h5/sign_custom/skins/normal-calendar-sign_190826/image/modal-thanks-img.2f89240f.png?x-oss-process=image/format,webp);
  background-size: 100% 100%;
  background-repeat: no-repeat;
}
.modal .modal-body.thanks .thanks-body .btn[data-v-b009b054] {
  width: 5.4rem;
  height: 1.213333rem;
  margin: 0 auto;
  border-radius: 0.613333rem;
  font-size: 0.48rem;
  font-family: PingFangSC;
  font-weight: 600;
  color: #ffffff;
  line-height: 1.213333rem;
  background: #fff;
}
.modal .modal-body.signErr[data-v-b009b054] {
  margin-top: -5.04rem;
}
.modal .modal-body.signErr .signErr-body[data-v-b009b054] {
  padding: 0.533333rem 0 0;
  height: 8rem;
  position: relative;
  text-align: center;
}
.modal .modal-body.signErr .signErr-body .header[data-v-b009b054] {
  height: 0.706667rem;
  font-size: 0.506667rem;
  font-family: PingFangSC;
  font-weight: 600;
  color: #333333;
  line-height: 0.706667rem;
}
.modal .modal-body.signErr .signErr-body .signErr-tip[data-v-b009b054] {
  margin-top: 0.32rem;
  height: 0.533333rem;
  font-size: 0.373333rem;
  font-family: PingFangSC;
  font-weight: 400;
  color: #666666;
  line-height: 0.533333rem;
}
.modal .modal-body.signErr .signErr-body .signErr-img[data-v-b009b054] {
  width: 4.986667rem;
  height: 2.853333rem;
  margin: 0.493333rem auto 0.453333rem;
  background: url(//yun.duiba.com.cn/h5/sign_custom/skins/normal-calendar-sign_190826/image/modal-signErr-img.16fda2c5.png);
  background-size: 100% 100%;
  background-repeat: no-repeat;
}
[duiba-webp='true'] .modal .modal-body.signErr .signErr-body .signErr-img[data-v-b009b054] {
  background-image: url(//yun.duiba.com.cn/h5/sign_custom/skins/normal-calendar-sign_190826/image/modal-signErr-img.16fda2c5.png?x-oss-process=image/format,webp);
  background-size: 100% 100%;
  background-repeat: no-repeat;
}
.modal .modal-body.signErr .signErr-body .btn[data-v-b009b054] {
  width: 5.4rem;
  height: 1.213333rem;
  margin: 0 auto;
  border-radius: 0.613333rem;
  font-size: 0.48rem;
  font-family: PingFangSC;
  font-weight: 600;
  color: #ffffff;
  line-height: 1.213333rem;
  background: #fff;
}
.modal .modal-body.coupon[data-v-b009b054],
.modal .modal-body.object[data-v-b009b054] {
  margin-top: -5.04rem;
}
.modal .modal-body.coupon .coupon-body[data-v-b009b054],
.modal .modal-body.object .coupon-body[data-v-b009b054],
.modal .modal-body.coupon .object-body[data-v-b009b054],
.modal .modal-body.object .object-body[data-v-b009b054] {
  padding: 0.533333rem 0 0;
  height: 8rem;
  position: relative;
  text-align: center;
}
.modal .modal-body.coupon .coupon-body .header[data-v-b009b054],
.modal .modal-body.object .coupon-body .header[data-v-b009b054],
.modal .modal-body.coupon .object-body .header[data-v-b009b054],
.modal .modal-body.object .object-body .header[data-v-b009b054] {
  height: 0.706667rem;
  font-size: 0.506667rem;
  font-family: PingFangSC;
  font-weight: 600;
  color: #333333;
  line-height: 0.706667rem;
}
.modal .modal-body.coupon .coupon-body .detail-img[data-v-b009b054],
.modal .modal-body.object .coupon-body .detail-img[data-v-b009b054],
.modal .modal-body.coupon .object-body .detail-img[data-v-b009b054],
.modal .modal-body.object .object-body .detail-img[data-v-b009b054] {
  width: 7.466667rem;
  height: 3.786667rem;
  margin: 0.28rem auto 0.173333rem;
  border-radius: 0.16rem;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -webkit-align-items: center;
  -ms-flex-align: center;
  align-items: center;
}
.modal .modal-body.coupon .coupon-body .detail-img img[data-v-b009b054],
.modal .modal-body.object .coupon-body .detail-img img[data-v-b009b054],
.modal .modal-body.coupon .object-body .detail-img img[data-v-b009b054],
.modal .modal-body.object .object-body .detail-img img[data-v-b009b054] {
  max-width: calc(100% - 0.266667rem);
  max-height: calc(100% - 0.266667rem);
}
.modal .modal-body.coupon .coupon-body .detail-title[data-v-b009b054],
.modal .modal-body.object .coupon-body .detail-title[data-v-b009b054],
.modal .modal-body.coupon .object-body .detail-title[data-v-b009b054],
.modal .modal-body.object .object-body .detail-title[data-v-b009b054] {
  height: 0.533333rem;
  font-size: 0.373333rem;
  font-family: PingFangSC;
  font-weight: 400;
  color: #333333;
  line-height: 0.533333rem;
}
.modal .modal-body.coupon .coupon-body .btn[data-v-b009b054],
.modal .modal-body.object .coupon-body .btn[data-v-b009b054],
.modal .modal-body.coupon .object-body .btn[data-v-b009b054],
.modal .modal-body.object .object-body .btn[data-v-b009b054] {
  display: block;
  width: 5.4rem;
  height: 1.213333rem;
  margin: 0.24rem auto 0;
  border-radius: 0.613333rem;
  font-size: 0.48rem;
  font-family: PingFangSC;
  font-weight: 600;
  color: #ffffff;
  line-height: 1.213333rem;
  background: #fff;
}
.modal .modal-body.coupon .coupon-body .prize-tip[data-v-b009b054],
.modal .modal-body.object .coupon-body .prize-tip[data-v-b009b054],
.modal .modal-body.coupon .object-body .prize-tip[data-v-b009b054],
.modal .modal-body.object .object-body .prize-tip[data-v-b009b054] {
  margin-top: 0.266667rem;
  height: 0.986667rem;
  font-size: 0.346667rem;
  font-family: PingFangSC;
  font-weight: 400;
  color: #f5a623;
  line-height: 0.493333rem;
}
.modal .modal-body.coupon .object-body[data-v-b009b054],
.modal .modal-body.object .object-body[data-v-b009b054] {
  height: 9rem;
}
.modal .modal-body.coupon .object-body .detail-img[data-v-b009b054],
.modal .modal-body.object .object-body .detail-img[data-v-b009b054] {
  width: 3.866667rem;
  height: 3.866667rem;
}
.modal .modal-body.login[data-v-b009b054] {
  margin-top: -5.04rem;
  width: 8.053333rem;
  right: calc(50% - 4.026667rem);
}
.modal .modal-body.login .login-body[data-v-b009b054] {
  padding: 1.2rem 0 0;
  height: 5.866667rem;
  position: relative;
  text-align: center;
}
.modal .modal-body.login .login-body .header[data-v-b009b054] {
  height: 0.706667rem;
  font-size: 0.506667rem;
  font-family: PingFangSC;
  font-weight: 600;
  color: #333333;
  line-height: 0.706667rem;
}
.modal .modal-body.login .login-body .login-tip[data-v-b009b054] {
  margin-top: 0.32rem;
  height: 0.533333rem;
  font-size: 0.373333rem;
  font-family: PingFangSC;
  font-weight: 400;
  color: #666666;
  line-height: 0.533333rem;
}
.modal .modal-body.login .login-body .btn[data-v-b009b054] {
  width: 5.4rem;
  height: 1.213333rem;
  margin: 1.12rem auto 0;
  border-radius: 0.613333rem;
  font-size: 0.48rem;
  font-family: PingFangSC;
  font-weight: 600;
  color: #ffffff;
  line-height: 1.213333rem;
  background: #fff;
}
.modal .modal-body.reSignConfirm[data-v-b009b054] {
  margin-top: -5.04rem;
  width: 8.053333rem;
  right: calc(50% - 4.026667rem);
}
.modal .modal-body.reSignConfirm .reSignConfirm-body[data-v-b009b054] {
  padding: 1.2rem 0 0;
  height: 5.866667rem;
  position: relative;
  text-align: center;
}
.modal .modal-body.reSignConfirm .reSignConfirm-body[data-v-b009b054]::before {
  content: '';
  position: absolute;
  width: 3.64rem;
  height: 2.013333rem;
  top: -1.133333rem;
  right: calc(50% - 1.813333rem);
  bottom: auto;
  left: auto;
  background: url(//yun.duiba.com.cn/h5/sign_custom/skins/normal-calendar-sign_190826/image/reSign-modal-icon.026d38ee.png);
  background-size: 100% 100%;
  background-repeat: no-repeat;
}
[duiba-webp='true'] .modal .modal-body.reSignConfirm .reSignConfirm-body[data-v-b009b054]::before {
  background-image: url(//yun.duiba.com.cn/h5/sign_custom/skins/normal-calendar-sign_190826/image/reSign-modal-icon.026d38ee.png?x-oss-process=image/format,webp);
  background-size: 100% 100%;
  background-repeat: no-repeat;
}
.modal .modal-body.reSignConfirm .reSignConfirm-body .header[data-v-b009b054] {
  height: 0.706667rem;
  font-size: 0.506667rem;
  font-family: PingFangSC;
  font-weight: 600;
  color: #333333;
  line-height: 0.706667rem;
}
.modal .modal-body.reSignConfirm .reSignConfirm-body .reSignConfirm-tip[data-v-b009b054] {
  margin-top: 0.32rem;
  height: 0.533333rem;
  font-size: 0.373333rem;
  font-family: PingFangSC;
  font-weight: 400;
  color: #666666;
  line-height: 0.533333rem;
}
.modal .modal-body.reSignConfirm .reSignConfirm-body .btn-group[data-v-b009b054] {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  -ms-flex-pack: justify;
  justify-content: space-between;
  width: 7.146667rem;
  margin: 1.12rem auto 0;
}
.modal .modal-body.reSignConfirm .reSignConfirm-body .btn[data-v-b009b054],
.modal .modal-body.reSignConfirm .reSignConfirm-body .btn-cancel[data-v-b009b054] {
  width: 3.466667rem;
  height: 1.213333rem;
  border-radius: 0.613333rem;
  font-size: 0.48rem;
  font-family: PingFangSC;
  font-weight: 600;
  color: #ffffff;
  line-height: 1.213333rem;
  background: #fff;
  border-style: solid;
  border-width: 0.026667rem;
}
.modal .modal-body.reSignConfirm .reSignConfirm-body .btn-cancel[data-v-b009b054] {
  height: 1.16rem;
  line-height: 1.16rem;
}
.modal .modal-body.reSignErr[data-v-b009b054] {
  margin-top: -5.04rem;
  width: 8.053333rem;
  right: calc(50% - 4.026667rem);
}
.modal .modal-body.reSignErr .reSignErr-body[data-v-b009b054] {
  padding: 1.2rem 0 0;
  height: 5.866667rem;
  position: relative;
  text-align: center;
}
.modal .modal-body.reSignErr .reSignErr-body[data-v-b009b054]::before {
  content: '';
  position: absolute;
  width: 3.64rem;
  height: 2.013333rem;
  top: -1.133333rem;
  right: calc(50% - 1.813333rem);
  bottom: auto;
  left: auto;
  background: url(//yun.duiba.com.cn/h5/sign_custom/skins/normal-calendar-sign_190826/image/reSign-modal-icon-gray.3bd73813.png);
  background-size: 100% 100%;
  background-repeat: no-repeat;
}
[duiba-webp='true'] .modal .modal-body.reSignErr .reSignErr-body[data-v-b009b054]::before {
  background-image: url(//yun.duiba.com.cn/h5/sign_custom/skins/normal-calendar-sign_190826/image/reSign-modal-icon-gray.3bd73813.png?x-oss-process=image/format,webp);
  background-size: 100% 100%;
  background-repeat: no-repeat;
}
.modal .modal-body.reSignErr .reSignErr-body .header[data-v-b009b054] {
  height: 0.706667rem;
  font-size: 0.506667rem;
  font-family: PingFangSC;
  font-weight: 600;
  color: #333333;
  line-height: 0.706667rem;
}
.modal .modal-body.reSignErr .reSignErr-body .reSignErr-tip[data-v-b009b054] {
  margin-top: 0.32rem;
  height: 0.533333rem;
  font-size: 0.373333rem;
  font-family: PingFangSC;
  font-weight: 400;
  color: #666666;
  line-height: 0.533333rem;
}
.modal .modal-body.reSignErr .reSignErr-body .btn[data-v-b009b054] {
  width: 5.4rem;
  height: 1.213333rem;
  margin: 1.12rem auto 0;
  border-radius: 0.613333rem;
  font-size: 0.48rem;
  font-family: PingFangSC;
  font-weight: 600;
  color: #ffffff;
  line-height: 1.213333rem;
  background: #fff;
}
