/**
 * 解析eval参数生成真正的token
 */

console.log('=== 解析eval参数生成token ===\n');

// 新的XOR数组 (从最新日志中提取)
const XOR_ARRAY = [600, 3427, 3559, 274, 2525];

console.log(`XOR数组: [${XOR_ARRAY.join(', ')}]`);

// 定义XOR函数
const _$YJ50 = (x) => x ^ XOR_ARRAY[0];  // x ^ 600
const _$ZTn = (x) => x ^ XOR_ARRAY[1];   // x ^ 3427
const _$OhV = (x) => x ^ XOR_ARRAY[2];   // x ^ 3559
const _$tMg = (x) => x ^ XOR_ARRAY[3];   // x ^ 274
const _$ShdX = (x) => x ^ XOR_ARRAY[4];  // x ^ 2525

// String.fromCharCode函数
const __gmibIrDac = String.fromCharCode;

console.log('\n定义的函数:');
console.log('_$YJ50(x) = x ^ 600');
console.log('_$ZTn(x) = x ^ 3427');
console.log('_$OhV(x) = x ^ 3559');
console.log('_$tMg(x) = x ^ 274');
console.log('_$ShdX(x) = x ^ 2525');

// 从日志中提取的eval参数示例 (简化版)
function parseEvalParameters() {
    console.log('\n解析eval参数:');
    
    // 从日志中提取的一些关键参数
    const evalParams = [
        32,  // __gmibIrDac(32) = ' '
        // 第一组参数
        -1-~(0x77^0), 105, 110, 0x6477>>4>>4, 111, ~119,
        // XOR函数调用
        _$ShdX(2438),
        0x2758>>4>>4,
        ~(0x65^-1),
        _$tMg(0x174),
        // 更多参数...
    ];
    
    console.log('计算一些关键参数:');
    
    // 计算示例参数
    console.log(`32 -> '${String.fromCharCode(32)}'`);
    console.log(`105 -> '${String.fromCharCode(105)}'`);
    console.log(`110 -> '${String.fromCharCode(110)}'`);
    console.log(`111 -> '${String.fromCharCode(111)}'`);
    
    // 计算XOR函数结果
    console.log(`_$ShdX(2438) = ${_$ShdX(2438)} -> '${String.fromCharCode(_$ShdX(2438))}'`);
    console.log(`_$tMg(0x174) = ${_$tMg(0x174)} -> '${String.fromCharCode(_$tMg(0x174))}'`);
    
    // 计算数学表达式
    const expr1 = -1-~(0x77^0);
    const expr2 = 0x6477>>4>>4;
    const expr3 = 0x2758>>4>>4;
    const expr4 = ~(0x65^-1);
    
    console.log(`-1-~(0x77^0) = ${expr1} -> '${String.fromCharCode(expr1)}'`);
    console.log(`0x6477>>4>>4 = ${expr2} -> '${String.fromCharCode(expr2)}'`);
    console.log(`0x2758>>4>>4 = ${expr3} -> '${String.fromCharCode(expr3)}'`);
    console.log(`~(0x65^-1) = ${expr4} -> '${String.fromCharCode(expr4)}'`);
}

// 尝试构建一个简化的token生成器
function generateTokenFromXOR() {
    console.log('\n基于XOR数组生成token:');
    
    // 方法1: 直接使用XOR数组生成6位token
    const chars = 'abcdefghijklmnopqrstuvwxyz0123456789';
    let token1 = '';
    
    for (let i = 0; i < 6; i++) {
        const xorValue = XOR_ARRAY[i % XOR_ARRAY.length];
        const index = xorValue % chars.length;
        token1 += chars[index];
    }
    
    console.log(`方法1 (直接映射): ${token1}`);
    
    // 方法2: 使用XOR数组的ASCII值
    let token2 = '';
    for (let i = 0; i < 6; i++) {
        const xorValue = XOR_ARRAY[i % XOR_ARRAY.length];
        // 将XOR值转换为ASCII范围
        const ascii = (xorValue % 94) + 33; // ASCII 33-126
        if (ascii >= 48 && ascii <= 122) { // 数字和字母范围
            token2 += String.fromCharCode(ascii);
        } else {
            token2 += chars[xorValue % chars.length];
        }
    }
    
    console.log(`方法2 (ASCII转换): ${token2}`);
    
    // 方法3: 基于XOR数组的哈希
    const hash = XOR_ARRAY.reduce((sum, val) => sum + val, 0);
    let token3 = '';
    for (let i = 0; i < 6; i++) {
        const index = (hash + XOR_ARRAY[i % XOR_ARRAY.length] + i * 7) % chars.length;
        token3 += chars[index];
    }
    
    console.log(`方法3 (哈希组合): ${token3}`);
    
    return [token1, token2, token3];
}

// 尝试模拟eval的执行
function simulateEvalExecution() {
    console.log('\n模拟eval执行:');
    
    // 基于日志中的参数模式，尝试构建一个简化的执行
    try {
        // 模拟一些关键的eval参数
        const params = [
            32, // 空格
            119, 105, 110, 100, 111, 119, // "window"
            91, 39, // ['
            // 这里应该有更多参数来生成完整的JavaScript代码
        ];
        
        let result = '';
        params.forEach(param => {
            if (typeof param === 'number' && param >= 32 && param <= 126) {
                result += String.fromCharCode(param);
            }
        });
        
        console.log(`部分解析结果: "${result}"`);
        
        // 如果eval最终执行的是类似 window['token'] = 'xxx' 的代码
        // 那么我们需要找到生成token值的部分
        
    } catch (error) {
        console.log(`模拟执行失败: ${error.message}`);
    }
}

// 基于新XOR数组的特征分析
function analyzeXORCharacteristics() {
    console.log('\n分析新XOR数组特征:');
    
    const oldArray = [2212, 53, 2852, 3445, 3327];
    const newArray = [600, 3427, 3559, 274, 2525];
    
    console.log(`旧数组: [${oldArray.join(', ')}]`);
    console.log(`新数组: [${newArray.join(', ')}]`);
    
    // 计算数组特征
    const oldSum = oldArray.reduce((sum, val) => sum + val, 0);
    const newSum = newArray.reduce((sum, val) => sum + val, 0);
    
    console.log(`旧数组和: ${oldSum}`);
    console.log(`新数组和: ${newSum}`);
    console.log(`差值: ${newSum - oldSum}`);
    
    // 如果token与数组和相关
    const chars = 'abcdefghijklmnopqrstuvwxyz0123456789';
    let tokenFromSum = '';
    for (let i = 0; i < 6; i++) {
        const index = (newSum + i * 13) % chars.length;
        tokenFromSum += chars[index];
    }
    
    console.log(`基于数组和生成的token: ${tokenFromSum}`);
    
    return tokenFromSum;
}

// 主函数
function main() {
    parseEvalParameters();
    const tokens = generateTokenFromXOR();
    simulateEvalExecution();
    const sumToken = analyzeXORCharacteristics();
    
    console.log('\n=== 生成的token候选 ===');
    console.log(`候选1: ${tokens[0]}`);
    console.log(`候选2: ${tokens[1]}`);
    console.log(`候选3: ${tokens[2]}`);
    console.log(`候选4: ${sumToken}`);
    
    console.log('\n=== 结论 ===');
    console.log('1. XOR数组确实是动态变化的');
    console.log('2. token应该基于新的XOR数组动态生成');
    console.log('3. 需要正确解析eval中的完整参数列表');
    console.log('4. 建议测试上述候选token');
}

// 运行分析
main();
