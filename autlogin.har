{"log": {"version": "1.2", "creator": {"name": "Reqable", "version": "2.33.12"}, "entries": [{"startedDateTime": "2025-08-05T06:19:04.704Z", "time": 88, "request": {"method": "GET", "url": "https://uc-api.skyallhere.com/miniprogram/tp/duiba-nologin?dbredirect=https%3A%2F%2F74367-1-activity.m.dexfu.cn%2Fsign%2Fcomponent%2Fpage%3FsignOperatingId%3D303855131763271", "httpVersion": "HTTP/1.1", "cookies": [], "headers": [{"name": "Host", "value": "uc-api.skyallhere.com"}, {"name": "Connection", "value": "keep-alive"}, {"name": "App-Path", "value": "/pages/user/user"}, {"name": "Authorization", "value": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJvcGVuaWQiOiJvTVNTZzRtR2dnc0hNUWp0TmdBVXNYLVJ1aUpzIiwidW5pb25pZCI6Im9BdWVHamtzTHdieFRPY2tGejAzS2I5NWExTzgiLCJ1c2VyaWQiOjE5NDYwMzc5LCJ1c2VyX2NvZGUiOiIxYWI4ODgxNGNjNDhmNzdmIiwidXNlcl9waG9uZSI6IjE4ODA4MzMzMzk2Iiwibmlja19uYW1lIjoiIiwiZXhwIjoxNzU0Mzc2NTMxfQ.rOAXDkwcQHswBIS6JH1UdvG4cjYwj7926DvSvfd1_dM"}, {"name": "User-Agent", "value": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF WindowsWechat(0x63090c37)XWEB/14315"}, {"name": "Content-Type", "value": "application/json"}, {"name": "App-System", "value": "Windows 10 x64"}, {"name": "xweb_xhr", "value": "1"}, {"name": "App-Model", "value": "microsoft"}, {"name": "App-Sdkversion", "value": "3.9.0"}, {"name": "App-Version", "value": "3.9.12"}, {"name": "Accept", "value": "*/*"}, {"name": "Sec-Fetch-Site", "value": "cross-site"}, {"name": "Sec-Fetch-Mode", "value": "cors"}, {"name": "Sec-Fetch-Dest", "value": "empty"}, {"name": "<PERSON><PERSON><PERSON>", "value": "https://servicewechat.com/wxff438d3c60c63fb6/342/page-frame.html"}, {"name": "Accept-Encoding", "value": "gzip, deflate, br"}, {"name": "Accept-Language", "value": "zh-CN,zh;q=0.9"}], "queryString": [{"name": "dbredirect", "value": "https%3A%2F%2F74367-1-activity.m.dexfu.cn%2Fsign%2Fcomponent%2Fpage%3FsignOperatingId%3D303855131763271"}], "headersSize": 1064, "bodySize": 0, "_status": "completed", "_startTimestamp": 1754374744704523, "_endTimestamp": 1754374744708291}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "cookies": [], "headers": [{"name": "Date", "value": "Tue, 05 Aug 2025 06:19:04 GMT"}, {"name": "Content-Type", "value": "application/json; charset=utf-8"}, {"name": "Content-Length", "value": "514"}, {"name": "Connection", "value": "keep-alive"}, {"name": "X-Request-Id", "value": "7c8e44fa50847ee7d7e47551ca412fb6"}, {"name": "Access-Control-Allow-Origin", "value": "*"}, {"name": "Access-Control-Allow-Credentials", "value": "true"}], "content": {"size": 514, "mimeType": "application/json", "text": "{\"code\":0,\"data\":\"https://74367-1-activity.m.dexfu.cn/autoLogin/autologin?sign=f9317128b95096706b8ee4c6152da567&uid=1ab88814cc48f77f&credits=305&timestamp=1754374744063&redirect=https%3A%2F%2F74367-1-activity.m.dexfu.cn%2Fsign%2Fcomponent%2Fpage%3FsignOperatingId%3D303855131763271&dcustom=avatar%3Dhttps%253A%252F%252Fobs-yonghuyunying.obs.cn-south-1.myhuaweicloud.com%252Fmember-center%252Fstatic%252Favatar-default.png%26nickname%3D%25E5%2595%25B5%25E5%2595%25B5&appKey=4KXh6ZM4FM8JuSBdanaBi61wK9w7\",\"msg\":\"ok\"}"}, "redirectURL": "", "headersSize": 249, "bodySize": 514, "_status": "completed", "_startTimestamp": 1754374744792311, "_endTimestamp": 1754374744792577}, "cache": {}, "timings": {"send": -1, "wait": -1, "receive": -1}, "serverIPAddress": "***********", "connection": "1012", "comment": "", "_id": 1031, "_uid": "9c8dcd4d-2cfb-4080-8d13-77a4a629d885", "_cid": 1012, "_ctime": 1754374744537, "_sid": 1, "_stime": 1754374744704, "_serverAddress": "***********", "_serverAddressFamily": 0, "_serverPort": 443, "_clientAddress": "127.0.0.1", "_clientAddressFamily": 0, "_clientPort": 62284, "_app": {"name": "WeChatAppEx", "id": "WeChatAppEx.exe", "path": "C:\\Users\\<USER>\\AppData\\Roaming\\Tencent\\WeChat\\XPlugin\\Plugins\\RadiumWMPF\\14315\\extracted\\runtime\\WeChatAppEx.exe", "stackTrace": null}}, {"startedDateTime": "2025-08-05T06:19:06.698Z", "time": 126, "request": {"method": "GET", "url": "https://74367-1-activity.m.dexfu.cn/autoLogin/autologin?sign=f9317128b95096706b8ee4c6152da567&uid=1ab88814cc48f77f&credits=305&timestamp=1754374744063&redirect=https%3A%2F%2F74367-1-activity.m.dexfu.cn%2Fsign%2Fcomponent%2Fpage%3FsignOperatingId%3D303855131763271&dcustom=avatar%3Dhttps%253A%252F%252Fobs-yonghuyunying.obs.cn-south-1.myhuaweicloud.com%252Fmember-center%252Fstatic%252Favatar-default.png%26nickname%3D%25E5%2595%25B5%25E5%2595%25B5&appKey=4KXh6ZM4FM8JuSBdanaBi61wK9w7", "httpVersion": "HTTP/2.0", "cookies": [], "headers": [{"name": ":method", "value": "GET"}, {"name": ":authority", "value": "74367-1-activity.m.dexfu.cn"}, {"name": ":path", "value": "/autoLogin/autologin?sign=f9317128b95096706b8ee4c6152da567&uid=1ab88814cc48f77f&credits=305&timestamp=1754374744063&redirect=https%3A%2F%2F74367-1-activity.m.dexfu.cn%2Fsign%2Fcomponent%2Fpage%3FsignOperatingId%3D303855131763271&dcustom=avatar%3Dhttps%253A%252F%252Fobs-yonghuyunying.obs.cn-south-1.myhuaweicloud.com%252Fmember-center%252Fstatic%252Favatar-default.png%26nickname%3D%25E5%2595%25B5%25E5%2595%25B5&appKey=4KXh6ZM4FM8JuSBdanaBi61wK9w7"}, {"name": ":scheme", "value": "https"}, {"name": "upgrade-insecure-requests", "value": "1"}, {"name": "user-agent", "value": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF WindowsWechat(0x63090c37)XWEB/14315"}, {"name": "accept", "value": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/wxpic,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7"}, {"name": "sec-fetch-site", "value": "none"}, {"name": "sec-fetch-mode", "value": "navigate"}, {"name": "sec-fetch-user", "value": "?1"}, {"name": "sec-fetch-dest", "value": "document"}, {"name": "accept-encoding", "value": "gzip, deflate, br"}, {"name": "accept-language", "value": "zh-CN,zh;q=0.9"}, {"name": "priority", "value": "u=0, i"}], "queryString": [{"name": "sign", "value": "f9317128b95096706b8ee4c6152da567"}, {"name": "uid", "value": "1ab88814cc48f77f"}, {"name": "credits", "value": "305"}, {"name": "timestamp", "value": "1754374744063"}, {"name": "redirect", "value": "https%3A%2F%2F74367-1-activity.m.dexfu.cn%2Fsign%2Fcomponent%2Fpage%3FsignOperatingId%3D303855131763271"}, {"name": "d<PERSON><PERSON>", "value": "avatar%3Dhttps%253A%252F%252Fobs-yonghuyunying.obs.cn-south-1.myhuaweicloud.com%252Fmember-center%252Fstatic%252Favatar-default.png%26nickname%3D%25E5%2595%25B5%25E5%2595%25B5"}, {"name": "appKey", "value": "4KXh6ZM4FM8JuSBdanaBi61wK9w7"}], "headersSize": 1151, "bodySize": 0, "_status": "completed", "_startTimestamp": 1754374746698666, "_endTimestamp": 1754374746699592}, "response": {"status": 302, "statusText": "", "httpVersion": "HTTP/2.0", "cookies": [{"name": "wdata4", "value": "x+FRzXhGTVb1pn0JcCMgpy6L8VfQebTfa5Esmc5L4/KxTzUp7osIz+10kb0DIO9egCQRiSI/prlRC0qXs45xSOxYEa6obgMg1BrKJ8xK4Nr0Vuz2cj8667Dpgcq+68PQVAJv1AcvYZFrX6vtNMV9Uw=="}, {"name": "w_ts", "value": "1754374746040"}, {"name": "_ac", "value": "eyJhaWQiOjc0MzY3LCJjaWQiOjQzMDYxODE1MjZ9"}, {"name": "tokenId", "value": "6b0a81db6750fed033e402e40e5111db"}, {"name": "wdata3", "value": "ugtvymFKM3XYjuJRP2BvbxNdcUQx1V98ufCoK1UecocWmH1vFK6tfArr4EtaxLw28oECPLgfbReuL3bFET9q2TRa4Nr4XxvYt7GGVhW7iqocksBFcyBPGQry789prbMjeKsjRYiMSo4rGr"}, {"name": "createdAtToday", "value": "false"}, {"name": "isNotLoginUser", "value": "false"}, {"name": "showNavBar", "value": ""}, {"name": "transfer", "value": ""}, {"name": "d<PERSON><PERSON>", "value": "avatar%3Dhttps%253A%252F%252Fobs-yonghuyunying.obs.cn-south-1.myhuaweicloud.com%252Fmember-center%252Fstatic%252Favatar-default.png%26nickname%3D%25E5%2595%25B5%25E5%2595%25B5"}, {"name": "comsumerJoinActivityId", "value": ""}], "headers": [{"name": ":status", "value": "302"}, {"name": "content-length", "value": "0"}, {"name": "location", "value": "https://74367-1-activity.m.dexfu.cn/sign/component/page?signOperatingId=303855131763271&from=login&spm=74367.1.1.1"}, {"name": "x-should-delimit", "value": "1"}, {"name": "set-cookie", "value": "wdata4=x+FRzXhGTVb1pn0JcCMgpy6L8VfQebTfa5Esmc5L4/KxTzUp7osIz+10kb0DIO9egCQRiSI/prlRC0qXs45xSOxYEa6obgMg1BrKJ8xK4Nr0Vuz2cj8667Dpgcq+68PQVAJv1AcvYZFrX6vtNMV9Uw==; Max-Age=86400; Expires=Wed, 06-Aug-2025 06:19:06 GMT; Domain=dexfu.cn; Path=/; HttpOnly"}, {"name": "set-cookie", "value": "w_ts=1754374746040; Max-Age=86400; Expires=Wed, 06-Aug-2025 06:19:06 GMT; Domain=dexfu.cn; Path=/; HttpOnly"}, {"name": "set-cookie", "value": "_ac=eyJhaWQiOjc0MzY3LCJjaWQiOjQzMDYxODE1MjZ9; Domain=dexfu.cn; Path=/; HttpOnly"}, {"name": "set-cookie", "value": "tokenId=6b0a81db6750fed033e402e40e5111db; Domain=dexfu.cn; Path=/"}, {"name": "set-cookie", "value": "wdata3=ugtvymFKM3XYjuJRP2BvbxNdcUQx1V98ufCoK1UecocWmH1vFK6tfArr4EtaxLw28oECPLgfbReuL3bFET9q2TRa4Nr4XxvYt7GGVhW7iqocksBFcyBPGQry789prbMjeKsjRYiMSo4rGr; Domain=dexfu.cn; Path=/"}, {"name": "set-cookie", "value": "createdAtToday=false; Max-Age=34853; Expires=Tue, 05-Aug-2025 15:59:59 GMT; Domain=dexfu.cn; Path=/"}, {"name": "set-cookie", "value": "isNotLoginUser=false; Max-Age=86400; Expires=Wed, 06-Aug-2025 06:19:06 GMT; Domain=dexfu.cn; Path=/"}, {"name": "set-cookie", "value": "showNavBar=; Max-Age=0; Expires=Thu, 01-Jan-1970 00:00:10 GMT; Domain=dexfu.cn; Path=/"}, {"name": "set-cookie", "value": "transfer=; Max-Age=0; Expires=Thu, 01-Jan-1970 00:00:10 GMT; Domain=dexfu.cn; Path=/"}, {"name": "set-cookie", "value": "dcustom=avatar%3Dhttps%253A%252F%252Fobs-yonghuyunying.obs.cn-south-1.myhuaweicloud.com%252Fmember-center%252Fstatic%252Favatar-default.png%26nickname%3D%25E5%2595%25B5%25E5%2595%25B5; Max-Age=86400; Expires=Wed, 06-Aug-2025 06:19:06 GMT; Domain=dexfu.cn; Path=/"}, {"name": "set-cookie", "value": "comsumerJoinActivityId=; Max-Age=0; Expires=Thu, 01-Jan-1970 00:00:10 GMT; Domain=dexfu.cn; Path=/"}, {"name": "p3p", "value": "CP=CURa ADMa DEVa PSAo PSDo OUR BUS UNI PUR INT DEM STA PRE COM NAV OTC NOI DSP COR"}, {"name": "content-language", "value": "zh-CN"}, {"name": "date", "value": "Tue, 05 Aug 2025 06:19:05 GMT"}, {"name": "server", "value": "<PERSON>"}, {"name": "x-service-id", "value": "CREDITS-HOME-WEB"}, {"name": "x-profile", "value": "prodvpc"}], "content": {"size": 0, "text": ""}, "redirectURL": "", "headersSize": 1955, "bodySize": 0, "_status": "completed", "_startTimestamp": 1754374746824398, "_endTimestamp": 1754374746824207}, "cache": {}, "timings": {"send": -1, "wait": -1, "receive": -1}, "serverIPAddress": "**************", "connection": "1019", "comment": "", "_id": 1038, "_uid": "0f008f45-bf7a-4c89-998c-0e7957b2def1", "_cid": 1019, "_ctime": 1754374745486, "_sid": 1, "_stime": 1754374745486, "_serverAddress": "**************", "_serverAddressFamily": 0, "_serverPort": 443, "_clientAddress": "127.0.0.1", "_clientAddressFamily": 0, "_clientPort": 62302, "_app": {"name": "WeChatAppEx", "id": "WeChatAppEx.exe", "path": "C:\\Users\\<USER>\\AppData\\Roaming\\Tencent\\WeChat\\XPlugin\\Plugins\\RadiumWMPF\\14315\\extracted\\runtime\\WeChatAppEx.exe", "stackTrace": null}}]}}