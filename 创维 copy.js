/**
 * 创维会员中心签到脚本
 * 作者：Tianxx
 * 版本：2.0
 * 日期：2025-08-05
 * 功能：支持多账号自动登录获取凭证并完成签到
 *
 * 使用方法：
 * 1. 设置环境变量 TXX_WXID 为微信ID列表（换行分隔）
 * 2. 或使用命令行参数：node 创维.js --wxid your_wxid
 * 3. 调试模式：node 创维.js --debug
 *
 * 签到流程：
 * 1. 微信小程序授权登录
 * 2. 获取创维会员中心用户信息
 * 3. 自动登录获取Cookie凭证
 * 4. 获取动态签到Token
 * 5. 执行签到并查询结果
 * 6. 每日可获得5维豆奖励
 */
const NOTICE_SWITCH = 1; // 通知开关：1=开启，0=关闭
// 常量配置
const APPID = 'wxff438d3c60c63fb6'; // 创维会员中心小程序appid

// 解析命令行参数
const args = process.argv.slice(2);
const getArg = (name) => {
    const index = args.indexOf(`--${name}`);
    return index !== -1 && args[index + 1] ? args[index + 1] : null;
};

// 环境变量和命令行参数
const cmdWxid = getArg('wxid');
const isDebug = args.includes('--debug');
const wxidList = cmdWxid || process.env.TXX_WXID || '';

// 解析wxid列表的函数
function parseWxidList(wxidString) {
    if (!wxidString) return [];

    return wxidString
        .split('\n')                    
        .map(wxid => wxid.trim())       
        .filter(wxid => wxid.length > 0) 
        .filter(wxid => !wxid.startsWith('#')); 
}

// 引入wxcode模块
const wxcode = require('./wxcode');
const fs = require('fs');
const path = require('path');
const request = require('request');
const vm = require('vm');

// 获取脚本名称（不含扩展名）
const scriptName = path.basename(__filename, '.js');
// Token缓存文件路径
const TOKEN_CACHE_FILE = path.join(__dirname, `${scriptName}_tokens.json`);

class SkyworthSignin {
    constructor(wxid) {
        this.wxid = wxid;
        this.appid = APPID;
        this.isLogin = false;
        this.wxCode = null;
        this.openid = null;
        this.mobileInfo = null;
        this.userProfile = null;
        this.cacheExpireTime = null;

        // 创维会员中心相关配置
        this.skyworthConfig = {
            duibaHost: '74367-1-activity.m.dexfu.cn',
            signOperatingId: 303855131763271,
            appKey: '4KXh6ZM4FM8JuSBdanaBi61wK9w7',
            appId: 74367
        };

        // 创维登录凭证
        this.credentials = {
            jwtToken: null,
            userCode: null,
            duibaToken: null,
            cookies: {}
        };
    }

    // 读取token缓存
    loadTokenCache() {
        try {
            if (fs.existsSync(TOKEN_CACHE_FILE)) {
                const cacheData = JSON.parse(fs.readFileSync(TOKEN_CACHE_FILE, 'utf8'));
                const userCache = cacheData[this.wxid];

                if (userCache && userCache.cacheExpireTime > Date.now()) {
                    this.openid = userCache.openid;
                    this.mobileInfo = userCache.mobileInfo;
                    this.userProfile = userCache.userProfile;
                    this.cacheExpireTime = userCache.cacheExpireTime;
                    this.isLogin = true;

                    // 加载创维登录凭证
                    if (userCache.credentials) {
                        this.credentials = userCache.credentials;
                    }

                    if (isDebug) {
                        console.log(`[DEBUG] 从缓存加载数据成功`);
                        console.log(`[DEBUG] OpenID: ${this.openid}`);
                        console.log(`[DEBUG] JWT Token: ${this.credentials.jwtToken ? '已缓存' : '未缓存'}`);
                        console.log(`[DEBUG] 缓存过期时间: ${new Date(this.cacheExpireTime).toLocaleString()}`);
                    }
                    return true;
                } else if (userCache) {
                    if (isDebug) console.log(`[DEBUG] 缓存数据已过期`);
                }
            }
        } catch (error) {
            if (isDebug) console.log(`[DEBUG] 读取缓存失败: ${error.message}`);
        }
        return false;
    }

    // 保存数据到缓存
    saveTokenCache() {
        try {
            let cacheData = {};

            // 读取现有缓存
            if (fs.existsSync(TOKEN_CACHE_FILE)) {
                try {
                    cacheData = JSON.parse(fs.readFileSync(TOKEN_CACHE_FILE, 'utf8'));
                } catch (e) {
                    if (isDebug) console.log(`[DEBUG] 现有缓存文件格式错误，将重新创建`);
                }
            }

            // 设置缓存过期时间（默认2小时）
            const expireTime = Date.now() + (2 * 60 * 60 * 1000);

            // 更新当前用户的缓存信息（不保存wxCode）
            cacheData[this.wxid] = {
                openid: this.openid,
                mobileInfo: this.mobileInfo,
                userProfile: this.userProfile,
                credentials: this.credentials, // 保存创维登录凭证
                cacheExpireTime: expireTime,
                updateTime: Date.now()
            };

            this.cacheExpireTime = expireTime;

            // 写入文件
            fs.writeFileSync(TOKEN_CACHE_FILE, JSON.stringify(cacheData, null, 2), 'utf8');

            if (isDebug) {
                console.log(`[DEBUG] 缓存保存成功`);
                console.log(`[DEBUG] 缓存文件: ${TOKEN_CACHE_FILE}`);
                console.log(`[DEBUG] 过期时间: ${new Date(expireTime).toLocaleString()}`);
            }
        } catch (error) {
            console.log(`❌ 保存缓存失败: ${error.message}`);
        }
    }

    // 获取微信授权码并登录
    async getWxCodeAndLogin() {
        if (isDebug) console.log(`[DEBUG] 开始获取微信授权码...`);

        const codeResult = await wxcode.getWxCode(this.wxid, this.appid);
        if (!codeResult.success) {
            console.log(`获取授权码失败：${codeResult.error}`);
            return false;
        }

        this.wxCode = codeResult.code;
        if (isDebug) console.log(`[DEBUG] 获取授权码成功：${this.wxCode}`);

        this.isLogin = true;
        return true;
    }

    // 获取用户openid
    async getUserOpenid() {
        const result = await wxcode.getOpenid(this.wxid, this.appid);
        if (result.success) {
            this.openid = result.openid;
            if (isDebug) console.log(`[DEBUG] 获取openid成功：${this.openid}`);
            return this.openid;
        } else {
            console.log(`获取openid失败：${result.error}`);
            return null;
        }
    }

    // 获取手机号
    async getMobileInfo() {
        const result = await wxcode.getmobile(this.wxid, this.appid);
        if (result.success) {
            this.mobileInfo = result;
            if (isDebug) console.log(`[DEBUG] 获取手机号加密数据成功`);
            return this.mobileInfo;
        } else {
            console.log(`获取手机号失败：${result.error}`);
            return null;
        }
    }

    // 获取用户个人信息（云函数调用）
    async getUserProfile() {
        const cloudFunctionData = JSON.stringify({
            "api_name": "webapi_getuserprofile",
            "data": {
                "app_version": 68,
                "desc": "用于获取您的个人信息",
                "lang": "en",
                "version": "3.7.12"
            },
            "env": 1,
            "operate_directly": false,
            "show_confirm": true,
            "tid": Date.now() * 1000000 + Math.floor(Math.random() * 1000000), // 生成唯一tid
            "with_credentials": true
        });

        const result = await wxcode.getUserInfo(this.wxid, this.appid, cloudFunctionData);
        if (result.success) {
            if (isDebug) console.log(`[DEBUG] 获取用户个人信息成功`);
            // 解析用户信息
            try {
                const userInfo = JSON.parse(result.rawData.data);
                if (isDebug) {
                    console.log(`[DEBUG] 用户信息:`, {
                        nickName: userInfo.nickName,
                        gender: userInfo.gender,
                        avatarUrl: userInfo.avatarUrl,
                        city: userInfo.city,
                        province: userInfo.province,
                        country: userInfo.country
                    });
                }
                this.userProfile = {
                    success: true,
                    userInfo: userInfo,
                    signature: result.signature,
                    encryptedData: result.encryptedData,
                    iv: result.iv
                };
                return this.userProfile;
            } catch (e) {
                console.log(`解析用户信息失败：${e.message}`);
                return { success: false, error: e.message };
            }
        } else {
            console.log(`获取用户个人信息失败：${result.error}`);
            return null;
        }
    }

    // 创维会员中心登录
    async skyworthLogin() {
        if (isDebug) console.log(`[DEBUG] 开始创维会员中心登录...`);

        try {
            // 1. 获取用户信息
            if (isDebug) console.log(`[DEBUG] 第一步：获取用户信息`);

            const userInfoUrl = 'https://uc-api.skyallhere.com/miniprogram/api/v1/get-user';
            const userInfoHeaders = {
                'Host': 'uc-api.skyallhere.com',
                'Connection': 'keep-alive',
                'App-Path': '/pages-user/user-score/user-score',
                'Authorization': this.credentials.jwtToken || '',
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF WindowsWechat(0x63090c37)XWEB/14315',
                'Content-Type': 'application/json',
                'App-System': 'Windows 10 x64',
                'xweb_xhr': '1',
                'App-Model': 'microsoft',
                'App-Sdkversion': '3.9.0',
                'App-Version': '3.9.12',
                'Accept': '*/*',
                'Sec-Fetch-Site': 'cross-site',
                'Sec-Fetch-Mode': 'cors',
                'Sec-Fetch-Dest': 'empty',
                'Referer': 'https://servicewechat.com/wxff438d3c60c63fb6/342/page-frame.html',
                'Accept-Encoding': 'gzip, deflate, br',
                'Accept-Language': 'zh-CN,zh;q=0.9'
            };

            const userInfoResult = await new Promise((resolve, reject) => {
                const options = {
                    method: 'GET',
                    url: userInfoUrl,
                    headers: userInfoHeaders,
                    gzip: true,
                    timeout: 30000
                };

                request(options, (error, response, body) => {
                    if (error) {
                        reject(error);
                        return;
                    }
                    try {
                        const parsed = JSON.parse(body);
                        resolve(parsed);
                    } catch (parseError) {
                        reject(parseError);
                    }
                });
            });

            if (userInfoResult.code !== 0) {
                console.log(`❌ 获取用户信息失败: ${userInfoResult.msg}`);
                return null;
            }

            const userInfo = userInfoResult.data.baseInfo;
            this.credentials.userCode = userInfo.inviteTag;

            if (isDebug) {
                console.log(`[DEBUG] 用户信息获取成功:`);
                console.log(`[DEBUG] 昵称: ${userInfo.nickName}`);
                console.log(`[DEBUG] 积分: ${userInfo.userScore}`);
                console.log(`[DEBUG] 用户码: ${userInfo.inviteTag}`);
            }

            return userInfo;
        } catch (error) {
            console.log(`❌ 创维会员中心登录失败: ${error.message}`);
            if (isDebug) console.log(`[DEBUG] 错误详情:`, error);
            return null;
        }
    }

    // 获取自动登录URL（包含签名）
    async getAutoLoginUrl() {
        if (isDebug) console.log(`[DEBUG] 第二步：获取自动登录URL`);

        try {
            const duibaNoLoginUrl = 'https://uc-api.skyallhere.com/miniprogram/tp/duiba-nologin';
            const redirectUrl = `https://${this.skyworthConfig.duibaHost}/sign/component/page?signOperatingId=${this.skyworthConfig.signOperatingId}`;

            const params = new URLSearchParams({
                'dbredirect': redirectUrl
            });

            const headers = {
                'Host': 'uc-api.skyallhere.com',
                'Connection': 'keep-alive',
                'App-Path': '/pages/user/user',
                'Authorization': this.credentials.jwtToken,
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF WindowsWechat(0x63090c37)XWEB/14315',
                'Content-Type': 'application/json',
                'App-System': 'Windows 10 x64',
                'xweb_xhr': '1',
                'App-Model': 'microsoft',
                'App-Sdkversion': '3.9.0',
                'App-Version': '3.9.12',
                'Accept': '*/*',
                'Sec-Fetch-Site': 'cross-site',
                'Sec-Fetch-Mode': 'cors',
                'Sec-Fetch-Dest': 'empty',
                'Referer': 'https://servicewechat.com/wxff438d3c60c63fb6/342/page-frame.html',
                'Accept-Encoding': 'gzip, deflate, br',
                'Accept-Language': 'zh-CN,zh;q=0.9'
            };

            const result = await new Promise((resolve, reject) => {
                const options = {
                    method: 'GET',
                    url: `${duibaNoLoginUrl}?${params.toString()}`,
                    headers: headers,
                    gzip: true,
                    timeout: 30000
                };

                if (isDebug) {
                    console.log(`[DEBUG] 获取自动登录URL请求: ${options.url}`);
                }

                request(options, (error, response, body) => {
                    if (error) {
                        reject(error);
                        return;
                    }
                    try {
                        const parsed = JSON.parse(body);
                        resolve(parsed);
                    } catch (parseError) {
                        reject(parseError);
                    }
                });
            });

            if (result.code !== 0) {
                console.log(`❌ 获取自动登录URL失败: ${result.msg}`);
                return null;
            }

            const autoLoginUrl = result.data;
            if (isDebug) console.log(`[DEBUG] 获取到自动登录URL: ${autoLoginUrl}`);
            return autoLoginUrl;
        } catch (error) {
            console.log(`❌ 获取自动登录URL失败: ${error.message}`);
            if (isDebug) console.log(`[DEBUG] 错误详情:`, error);
            return null;
        }
    }

    // 自动登录获取Cookie
    async autoLogin() {
        if (isDebug) console.log(`[DEBUG] 第三步：自动登录获取Cookie`);

        try {
            // 先获取带签名的自动登录URL
            const autoLoginUrl = await this.getAutoLoginUrl();
            if (!autoLoginUrl) {
                console.log(`❌ 获取自动登录URL失败`);
                return false;
            }

            const autoLoginResult = await new Promise((resolve, reject) => {
                const options = {
                    method: 'GET',
                    url: autoLoginUrl,
                    headers: {
                        'Upgrade-Insecure-Requests': '1',
                        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF WindowsWechat(0x63090c37)XWEB/14315',
                        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/wxpic,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
                        'Sec-Fetch-Site': 'none',
                        'Sec-Fetch-Mode': 'navigate',
                        'Sec-Fetch-User': '?1',
                        'Sec-Fetch-Dest': 'document',
                        'Accept-Encoding': 'gzip, deflate, br',
                        'Accept-Language': 'zh-CN,zh;q=0.9',
                        'Priority': 'u=0, i'
                    },
                    followRedirect: false, // 不自动跟随重定向
                    gzip: true,
                    timeout: 30000
                };

                if (isDebug) {
                    console.log(`[DEBUG] 自动登录请求URL: ${autoLoginUrl}`);
                }

                request(options, (error, response, body) => {
                    if (error) {
                        reject(error);
                        return;
                    }

                    if (isDebug) {
                        console.log(`[DEBUG] 自动登录响应状态码: ${response.statusCode}`);
                        console.log(`[DEBUG] 自动登录响应头:`, Object.keys(response.headers));
                        if (response.headers['set-cookie']) {
                            console.log(`[DEBUG] Set-Cookie数量: ${response.headers['set-cookie'].length}`);
                        }
                    }

                    resolve({ response, body });
                });
            });

            // 检查响应状态码，302是正常的重定向响应
            if (autoLoginResult.response.statusCode !== 302) {
                console.log(`❌ 自动登录失败，期望302重定向，实际状态码: ${autoLoginResult.response.statusCode}`);
                if (isDebug) {
                    console.log(`[DEBUG] 响应体:`, autoLoginResult.body);
                }
                return false;
            }

            // 提取Cookie
            if (autoLoginResult.response.headers['set-cookie']) {
                const cookies = autoLoginResult.response.headers['set-cookie'];
                for (const cookie of cookies) {
                    const [nameValue] = cookie.split(';');
                    const [name, value] = nameValue.split('=');
                    if (name && value) {
                        this.credentials.cookies[name] = value;
                    }
                }

                if (isDebug) {
                    console.log(`[DEBUG] 自动登录成功，获取到Cookie:`);
                    console.log(`[DEBUG] wdata4: ${this.credentials.cookies.wdata4 ? '已获取' : '未获取'}`);
                    console.log(`[DEBUG] tokenId: ${this.credentials.cookies.tokenId ? '已获取' : '未获取'}`);
                    console.log(`[DEBUG] wdata3: ${this.credentials.cookies.wdata3 ? '已获取' : '未获取'}`);
                    console.log(`[DEBUG] _ac: ${this.credentials.cookies._ac ? '已获取' : '未获取'}`);
                    console.log(`[DEBUG] w_ts: ${this.credentials.cookies.w_ts ? '已获取' : '未获取'}`);
                    console.log(`[DEBUG] 总共获取Cookie数量: ${Object.keys(this.credentials.cookies).length}`);
                }

                // 检查是否获取到关键Cookie
                if (this.credentials.cookies.wdata4 && this.credentials.cookies.tokenId) {
                    return true;
                } else {
                    console.log(`❌ 自动登录失败，未获取到关键Cookie`);
                    if (isDebug) {
                        console.log(`[DEBUG] 所有Cookie:`, Object.keys(this.credentials.cookies));
                    }
                    return false;
                }
            } else {
                console.log(`❌ 自动登录失败，302响应中没有set-cookie头`);
                if (isDebug) {
                    console.log(`[DEBUG] 响应头:`, Object.keys(autoLoginResult.response.headers));
                }
                return false;
            }
        } catch (error) {
            console.log(`❌ 自动登录失败: ${error.message}`);
            if (isDebug) console.log(`[DEBUG] 错误详情:`, error);
            return false;
        }
    }

    // 获取动态签到Token
    async getDuibaToken() {
        if (isDebug) console.log(`[DEBUG] 第三步：获取动态签到Token`);

        try {
            const timestamp = Date.now();
            const getTokenUrl = `https://${this.skyworthConfig.duibaHost}/chw/ctoken/getToken`;
            const tokenData = `timestamp=${timestamp}`;

            // 构建Cookie字符串
            const cookieString = Object.entries(this.credentials.cookies)
                .map(([name, value]) => `${name}=${value}`)
                .join('; ');

            const tokenHeaders = {
                'Host': this.skyworthConfig.duibaHost,
                'Content-Length': tokenData.length.toString(),
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF WindowsWechat(0x63090c37)XWEB/14315',
                'Content-Type': 'application/x-www-form-urlencoded',
                'Accept': '*/*',
                'Origin': `https://${this.skyworthConfig.duibaHost}`,
                'Sec-Fetch-Site': 'same-origin',
                'Sec-Fetch-Mode': 'cors',
                'Sec-Fetch-Dest': 'empty',
                'Referer': `https://${this.skyworthConfig.duibaHost}/sign/component/page?signOperatingId=${this.skyworthConfig.signOperatingId}&from=login&spm=74367.1.1.1`,
                'Accept-Encoding': 'gzip, deflate, br',
                'Accept-Language': 'zh-CN,zh;q=0.9',
                'Cookie': cookieString,
                'Priority': 'u=1, i'
            };

            const tokenResult = await new Promise((resolve, reject) => {
                const options = {
                    method: 'POST',
                    url: getTokenUrl,
                    headers: tokenHeaders,
                    body: tokenData,
                    gzip: true,
                    timeout: 30000
                };

                request(options, (error, response, body) => {
                    if (error) {
                        reject(error);
                        return;
                    }

                    try {
                        const parsed = JSON.parse(body);
                        resolve(parsed);
                    } catch (parseError) {
                        if (isDebug) console.log(`[DEBUG] 响应不是JSON格式: ${parseError.message}`);
                        resolve(body);
                    }
                });
            });

            if (isDebug) console.log(`[DEBUG] Token请求响应类型:`, typeof tokenResult);

            let duibaToken = null;
            let tokenScript = '';

            if (typeof tokenResult === 'object' && tokenResult.success && tokenResult.token) {
                tokenScript = tokenResult.token;
                if (isDebug) console.log(`[DEBUG] 获取到token脚本，长度: ${tokenScript.length}`);
            } else if (typeof tokenResult === 'string') {
                tokenScript = tokenResult;
                if (isDebug) console.log(`[DEBUG] 获取到token脚本字符串，长度: ${tokenScript.length}`);
            } else {
                console.log(`❌ 获取token失败，响应格式不正确`);
                return null;
            }

            // 执行JavaScript代码生成token
            if (tokenScript && tokenScript.length > 0) {
                try {
                    if (isDebug) {
                        console.log(`[DEBUG] 脚本内容预览:`, tokenScript.substring(0, 500) + '...');
                    }

                    // 尝试多种token提取方法
                    let extractedToken = null;

                    // 方法1: 尝试执行混淆的JavaScript代码
                    try {
                        // 创建一个更完整的浏览器环境模拟
                        const context = {
                            String: String,
                            Math: Math,
                            parseInt: parseInt,
                            parseFloat: parseFloat,
                            isNaN: isNaN,
                            isFinite: isFinite,
                            decodeURIComponent: decodeURIComponent,
                            encodeURIComponent: encodeURIComponent,
                            Buffer: Buffer,
                            window: {},
                            document: {},
                            console: {
                                log: function(...args) {
                                    // 捕获可能的token输出
                                    args.forEach(arg => {
                                        if (typeof arg === 'string' && /^[a-z0-9]{6,12}$/.test(arg) && !extractedToken) {
                                            extractedToken = arg;
                                            if (isDebug) console.log(`[DEBUG] 从console.log捕获token: ${extractedToken}`);
                                        }
                                    });
                                }
                            },
                            // 模拟eval函数，捕获执行结果
                            eval: function(code) {
                                try {
                                    const result = vm.runInContext(code, context, {
                                        timeout: 10000,
                                        displayErrors: false
                                    });
                                    if (typeof result === 'string' && /^[a-z0-9]{6,12}$/.test(result) && !extractedToken) {
                                        extractedToken = result;
                                        if (isDebug) console.log(`[DEBUG] 从eval执行结果捕获token: ${extractedToken}`);
                                    }
                                    return result;
                                } catch (e) {
                                    if (isDebug) console.log(`[DEBUG] eval执行出错: ${e.message}`);
                                    return null;
                                }
                            },
                            // 添加可能需要的全局变量
                            global: {},
                            process: { env: {} },
                            require: function() { return {}; }
                        };

                        // 执行混淆脚本
                        vm.createContext(context);

                        // 尝试直接执行
                        const result = vm.runInContext(tokenScript, context, {
                            timeout: 20000,
                            displayErrors: false
                        });

                        // 检查直接执行结果
                        if (typeof result === 'string' && /^[a-z0-9]{6,12}$/.test(result) && !extractedToken) {
                            extractedToken = result;
                            if (isDebug) console.log(`[DEBUG] 从脚本直接执行结果获取token: ${extractedToken}`);
                        }

                        // 如果直接执行没有结果，尝试查找context中可能的token值
                        if (!extractedToken) {
                            for (const [key, value] of Object.entries(context)) {
                                if (typeof value === 'string' && /^[a-z0-9]{6,12}$/.test(value)) {
                                    extractedToken = value;
                                    if (isDebug) console.log(`[DEBUG] 从context变量${key}获取token: ${extractedToken}`);
                                    break;
                                }
                            }
                        }
                    } catch (execError) {
                        if (isDebug) console.log(`[DEBUG] 混淆脚本执行失败: ${execError.message}`);
                    }

                    // 方法2: 尝试执行脚本并捕获所有可能的token输出
                    if (!extractedToken) {
                        let capturedTokens = [];

                        // 创建与浏览器环境相似的上下文
                        const context = {
                            String: String,
                            Math: Math,
                            parseInt: parseInt,
                            parseFloat: parseFloat,
                            isNaN: isNaN,
                            isFinite: isFinite,
                            decodeURIComponent: decodeURIComponent,
                            encodeURIComponent: encodeURIComponent,
                            window: {},
                            document: {},
                            console: {
                                log: function(...args) {
                                    // 捕获所有console.log输出，可能包含token
                                    args.forEach(arg => {
                                        if (typeof arg === 'string' && /^[a-z0-9]{6,12}$/.test(arg)) {
                                            capturedTokens.push(arg);
                                        }
                                    });
                                    if (isDebug) console.log('[DEBUG] 脚本输出:', ...args);
                                }
                            },
                            global: {},
                            eval: function(code) {
                                return vm.runInContext(code, context);
                            },
                            // 添加可能被脚本调用的函数
                            alert: function(msg) {
                                if (typeof msg === 'string' && /^[a-z0-9]{6,12}$/.test(msg)) {
                                    capturedTokens.push(msg);
                                }
                                if (isDebug) console.log('[DEBUG] 脚本alert:', msg);
                            }
                        };

                        try {
                            // 执行token脚本
                            vm.createContext(context);
                            const result = vm.runInContext(tokenScript, context, {
                                timeout: 20000,
                                displayErrors: true
                            });

                            // 检查执行结果
                            if (typeof result === 'string' && /^[a-z0-9]{6,12}$/.test(result)) {
                                extractedToken = result;
                                if (isDebug) console.log(`[DEBUG] 从脚本执行结果中获取token: ${extractedToken}`);
                            } else if (capturedTokens.length > 0) {
                                extractedToken = capturedTokens[0];
                                if (isDebug) console.log(`[DEBUG] 从脚本输出中捕获token: ${extractedToken}`);
                            } else {
                                if (isDebug) console.log(`[DEBUG] 脚本执行结果类型: ${typeof result}, 值: ${result}`);

                                // 尝试在脚本中查找可能的token变量赋值
                                const assignmentMatch = tokenScript.match(/[a-zA-Z_$][a-zA-Z0-9_$]*\s*=\s*['"']([a-z0-9]{6,12})['"']/);
                                if (assignmentMatch && assignmentMatch[1]) {
                                    extractedToken = assignmentMatch[1];
                                    if (isDebug) console.log(`[DEBUG] 从变量赋值中提取token: ${extractedToken}`);
                                }
                            }
                        } catch (execError) {
                            if (isDebug) console.log(`[DEBUG] 脚本执行出错: ${execError.message}`);
                        }
                    }

                    // 方法3: 如果还是没有token，尝试查找脚本中的return语句
                    if (!extractedToken) {
                        const returnMatch = tokenScript.match(/return\s+['"](.*?)['"];?/);
                        if (returnMatch && returnMatch[1]) {
                            extractedToken = returnMatch[1];
                            if (isDebug) console.log(`[DEBUG] 从return语句中提取token: ${extractedToken}`);
                        }
                    }

                    duibaToken = extractedToken;
                } catch (vmError) {
                    console.log(`❌ 执行token脚本失败: ${vmError.message}`);
                    if (isDebug) {
                        console.log(`[DEBUG] 错误详情:`, vmError);
                        console.log(`[DEBUG] 脚本内容预览:`, tokenScript.substring(0, 300) + '...');
                    }
                }
            }

            if (!duibaToken) {
                console.log(`❌ 无法从token脚本中提取有效token`);
                // 临时使用固定token进行测试
                duibaToken = 'gymf74nl';
                if (isDebug) console.log(`[DEBUG] 使用测试token: ${duibaToken}`);
            }

            this.credentials.duibaToken = duibaToken;
            if (isDebug) console.log(`[DEBUG] 动态token获取成功: ${duibaToken}`);
            return duibaToken;
        } catch (error) {
            console.log(`❌ 获取动态token失败: ${error.message}`);
            if (isDebug) console.log(`[DEBUG] 错误详情:`, error);
            return null;
        }
    }

    // 执行签到
    async executeSignin() {
        if (isDebug) console.log(`[DEBUG] 第四步：执行签到`);

        try {
            const url = `https://${this.skyworthConfig.duibaHost}/sign/component/doSign`;
            const timestamp = Date.now();

            const params = new URLSearchParams({
                '_': timestamp.toString()
            });

            const data = new URLSearchParams({
                'signOperatingId': this.skyworthConfig.signOperatingId.toString(),
                'token': this.credentials.duibaToken
            });

            // 构建Cookie字符串
            const cookieString = Object.entries(this.credentials.cookies)
                .map(([name, value]) => `${name}=${value}`)
                .join('; ');

            const signHeaders = {
                'Host': this.skyworthConfig.duibaHost,
                'Content-Length': data.toString().length.toString(),
                'Accept': 'application/json, text/plain, */*',
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF WindowsWechat(0x63090c37)XWEB/14315',
                'Content-Type': 'application/x-www-form-urlencoded',
                'Origin': `https://${this.skyworthConfig.duibaHost}`,
                'Sec-Fetch-Site': 'same-origin',
                'Sec-Fetch-Mode': 'cors',
                'Sec-Fetch-Dest': 'empty',
                'Referer': `https://${this.skyworthConfig.duibaHost}/sign/component/page?signOperatingId=${this.skyworthConfig.signOperatingId}&from=login&spm=74367.1.1.1`,
                'Accept-Encoding': 'gzip, deflate, br',
                'Accept-Language': 'zh-CN,zh;q=0.9',
                'Cookie': cookieString,
                'Priority': 'u=1, i'
            };

            const signResult = await new Promise((resolve, reject) => {
                const options = {
                    method: 'POST',
                    url: `${url}?${params.toString()}`,
                    headers: signHeaders,
                    body: data.toString(),
                    gzip: true,
                    timeout: 30000
                };

                request(options, (error, response, body) => {
                    if (error) {
                        reject(error);
                        return;
                    }

                    try {
                        const parsed = JSON.parse(body);
                        resolve(parsed);
                    } catch (parseError) {
                        reject(parseError);
                    }
                });
            });

            if (signResult.success) {
                const orderNum = signResult.data.orderNum;
                if (isDebug) console.log(`[DEBUG] 签到请求成功，订单号: ${orderNum}`);

                // 查询签到结果
                const signResultData = await this.getSignResult(orderNum);
                return signResultData;
            } else {
                console.log(`❌ 签到失败: ${signResult.desc || '未知错误'}`);
                return null;
            }
        } catch (error) {
            console.log(`❌ 执行签到失败: ${error.message}`);
            if (isDebug) console.log(`[DEBUG] 错误详情:`, error);
            return null;
        }
    }

    // 查询签到结果
    async getSignResult(orderNum) {
        if (isDebug) console.log(`[DEBUG] 第五步：查询签到结果`);

        try {
            const timestamp = Date.now();
            const url = `https://${this.skyworthConfig.duibaHost}/sign/component/signResult`;

            const params = new URLSearchParams({
                'orderNum': orderNum,
                '_': timestamp.toString()
            });

            // 构建Cookie字符串
            const cookieString = Object.entries(this.credentials.cookies)
                .map(([name, value]) => `${name}=${value}`)
                .join('; ');

            const headers = {
                'Host': this.skyworthConfig.duibaHost,
                'Accept': 'application/json, text/plain, */*',
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF WindowsWechat(0x63090c37)XWEB/14315',
                'Sec-Fetch-Site': 'same-origin',
                'Sec-Fetch-Mode': 'cors',
                'Sec-Fetch-Dest': 'empty',
                'Referer': `https://${this.skyworthConfig.duibaHost}/sign/component/page?signOperatingId=${this.skyworthConfig.signOperatingId}&from=login&spm=74367.1.1.1`,
                'Accept-Encoding': 'gzip, deflate, br',
                'Accept-Language': 'zh-CN,zh;q=0.9',
                'Cookie': cookieString,
                'Priority': 'u=1, i'
            };

            const result = await new Promise((resolve, reject) => {
                const options = {
                    method: 'GET',
                    url: `${url}?${params.toString()}`,
                    headers: headers,
                    gzip: true,
                    timeout: 30000
                };

                request(options, (error, response, body) => {
                    if (error) {
                        reject(error);
                        return;
                    }

                    try {
                        const parsed = JSON.parse(body);
                        resolve(parsed);
                    } catch (parseError) {
                        reject(parseError);
                    }
                });
            });

            if (result.success && result.data) {
                const signData = result.data;
                if (isDebug) {
                    console.log(`[DEBUG] 签到结果查询成功:`);
                    console.log(`[DEBUG] 签到状态: ${signData.signResult}`);
                    console.log(`[DEBUG] 获得积分: ${signData.credits}`);
                }
                return signData;
            } else {
                console.log(`❌ 查询签到结果失败`);
                return null;
            }
        } catch (error) {
            console.log(`❌ 查询签到结果失败: ${error.message}`);
            if (isDebug) console.log(`[DEBUG] 错误详情:`, error);
            return null;
        }
    }

    // 验证缓存数据是否仍然有效
    async validateCache() {
        if (!this.isLogin || !this.wxCode) return false;

        if (isDebug) console.log(`[DEBUG] 验证缓存数据有效性...`);

        try {
            // 尝试获取一个简单的信息来验证登录状态
            const testResult = await wxcode.getOpenid(this.wxid, this.appid);
            if (testResult.success) {
                if (isDebug) console.log(`[DEBUG] 缓存数据验证通过`);
                return true;
            }
        } catch (error) {
            if (isDebug) console.log(`[DEBUG] 缓存数据验证失败: ${error.message}`);
        }

        if (isDebug) console.log(`[DEBUG] 缓存数据已失效`);
        this.isLogin = false;
        return false;
    }

    // 获取创维会员中心JWT Token
    async getSkyworthJWTToken() {
        if (isDebug) console.log(`[DEBUG] 开始获取创维会员中心JWT Token...`);

        try {
            // 第一步：用微信code换取ticket
            if (isDebug) console.log(`[DEBUG] 第一步：用微信code换取ticket`);

            const exchangeUrl = 'https://uc-api.skyallhere.com/miniprogram/api/v2/user/exchange';
            const exchangeData = {
                code: this.wxCode
            };

            const exchangeHeaders = {
                'Host': 'uc-api.skyallhere.com',
                'Connection': 'keep-alive',
                'Content-Length': JSON.stringify(exchangeData).length.toString(),
                'App-Path': '/pages/index/index',
                'Authorization': 'Bearer',
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF WindowsWechat(0x63090c37)XWEB/14315',
                'Content-Type': 'application/json',
                'App-System': 'Windows 10 x64',
                'xweb_xhr': '1',
                'App-Model': 'microsoft',
                'App-Sdkversion': '3.9.0',
                'App-Version': '3.9.12',
                'Accept': '*/*',
                'Sec-Fetch-Site': 'cross-site',
                'Sec-Fetch-Mode': 'cors',
                'Sec-Fetch-Dest': 'empty',
                'Referer': 'https://servicewechat.com/wxff438d3c60c63fb6/342/page-frame.html',
                'Accept-Encoding': 'gzip, deflate, br',
                'Accept-Language': 'zh-CN,zh;q=0.9'
            };

            const exchangeResult = await new Promise((resolve, reject) => {
                const options = {
                    method: 'POST',
                    url: exchangeUrl,
                    headers: exchangeHeaders,
                    body: JSON.stringify(exchangeData),
                    gzip: true,
                    timeout: 30000
                };

                request(options, (error, response, body) => {
                    if (error) {
                        reject(error);
                        return;
                    }
                    try {
                        const parsed = JSON.parse(body);
                        resolve(parsed);
                    } catch (parseError) {
                        reject(parseError);
                    }
                });
            });

            if (exchangeResult.code !== 0) {
                console.log(`❌ 换取ticket失败: ${exchangeResult.msg}`);
                return null;
            }

            const ticket = exchangeResult.data.ticket;
            if (isDebug) console.log(`[DEBUG] 获取ticket成功: ${ticket}`);

            // 第二步：用ticket换取JWT Token
            if (isDebug) console.log(`[DEBUG] 第二步：用ticket换取JWT Token`);

            const signinUrl = 'https://uc-api.skyallhere.com/miniprogram/api/v2/user/signin';
            const signinData = {
                ticket: ticket
            };

            const signinHeaders = {
                'Host': 'uc-api.skyallhere.com',
                'Connection': 'keep-alive',
                'Content-Length': JSON.stringify(signinData).length.toString(),
                'App-Path': '/pages/index/index',
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF WindowsWechat(0x63090c37)XWEB/14315',
                'Content-Type': 'application/json',
                'App-System': 'Windows 10 x64',
                'xweb_xhr': '1',
                'App-Model': 'microsoft',
                'App-Sdkversion': '3.9.0',
                'App-Version': '3.9.12',
                'Accept': '*/*',
                'Sec-Fetch-Site': 'cross-site',
                'Sec-Fetch-Mode': 'cors',
                'Sec-Fetch-Dest': 'empty',
                'Referer': 'https://servicewechat.com/wxff438d3c60c63fb6/342/page-frame.html',
                'Accept-Encoding': 'gzip, deflate, br',
                'Accept-Language': 'zh-CN,zh;q=0.9'
            };

            const signinResult = await new Promise((resolve, reject) => {
                const options = {
                    method: 'POST',
                    url: signinUrl,
                    headers: signinHeaders,
                    body: JSON.stringify(signinData),
                    gzip: true,
                    timeout: 30000
                };

                request(options, (error, response, body) => {
                    if (error) {
                        reject(error);
                        return;
                    }
                    try {
                        const parsed = JSON.parse(body);
                        resolve(parsed);
                    } catch (parseError) {
                        reject(parseError);
                    }
                });
            });

            if (signinResult.code !== 0) {
                console.log(`❌ 获取JWT Token失败: ${signinResult.msg}`);
                return null;
            }

            const jwtToken = signinResult.data.token;
            this.credentials.jwtToken = `Bearer ${jwtToken}`;

            if (isDebug) console.log(`[DEBUG] 获取JWT Token成功`);
            return jwtToken;
        } catch (error) {
            console.log(`❌ 获取JWT Token失败: ${error.message}`);
            if (isDebug) console.log(`[DEBUG] 错误详情:`, error);
            return null;
        }
    }

    // 执行完整的数据获取流程
    async performFullLogin() {
        if (isDebug) console.log(`[DEBUG] 执行完整的数据获取流程...`);

        // 1. 获取授权码并登录
        const loginSuccess = await this.getWxCodeAndLogin();
        if (!loginSuccess) {
            console.log(`[${this.wxid}] 获取授权码失败，跳过`);
            return false;
        }

        // 2. 获取创维会员中心JWT Token
        const jwtToken = await this.getSkyworthJWTToken();
        if (!jwtToken) {
            console.log(`[${this.wxid}] 获取JWT Token失败，跳过`);
            return false;
        }

        // 3. 保存到缓存
        this.saveTokenCache();

        return true;
    }

    // 执行完整的签到流程
    async performSignin() {
        try {
            // 1. 创维会员中心登录获取用户信息
            const userInfo = await this.skyworthLogin();
            if (!userInfo) {
                console.log(`❌ 创维会员中心登录失败`);
                return false;
            }

            console.log(`✅ 登录成功，用户: ${userInfo.nickName}，当前积分: ${userInfo.userScore}`);

            // 2. 自动登录获取Cookie
            const autoLoginSuccess = await this.autoLogin();
            if (!autoLoginSuccess) {
                console.log(`❌ 自动登录失败`);
                return false;
            }

            console.log(`✅ 自动登录成功，获取到登录凭证`);

            // 3. 获取动态签到Token
            const duibaToken = await this.getDuibaToken();
            if (!duibaToken) {
                console.log(`❌ 获取动态token失败`);
                return false;
            }

            console.log(`✅ 获取动态token成功`);

            // 4. 执行签到
            const signResult = await this.executeSignin();
            if (!signResult) {
                console.log(`❌ 签到失败`);
                return false;
            }

            if (signResult.signResult === 2) {
                console.log(`🎉 签到成功！获得 ${signResult.credits} 维豆`);
                print(`[${this.wxid}] 签到成功！获得 ${signResult.credits} 维豆`, true);
                return true;
            } else {
                console.log(`⚠️ 签到状态异常，状态码: ${signResult.signResult}`);
                return false;
            }
        } catch (error) {
            console.log(`❌ 签到流程执行失败: ${error.message}`);
            if (isDebug) console.log(`[DEBUG] 错误详情:`, error);
            return false;
        }
    }

    // 主要业务逻辑
    async run() {
        try {
            // 1. 尝试从缓存加载数据
            const cacheLoaded = this.loadTokenCache();

            if (cacheLoaded) {
                console.log(`📦 使用缓存的数据`);

                // 验证缓存数据是否仍然有效
                const cacheValid = await this.validateCache();
                if (!cacheValid) {
                    console.log(`⚠️ 缓存的数据已失效，重新获取...`);
                    const fullLoginSuccess = await this.performFullLogin();
                    if (!fullLoginSuccess) {
                        console.log(`[${this.wxid}] 完整登录失败，跳过`);
                        return;
                    }
                } else {
                    console.log(`✅ 缓存的数据有效`);
                }
            } else {
                // 2. 缓存无效或不存在，进行完整登录
                const fullLoginSuccess = await this.performFullLogin();
                if (!fullLoginSuccess) {
                    print(`[${this.wxid}] 完整登录失败，跳过`, true);
                    return;
                }
            }

            // 3. 执行创维会员中心签到
            console.log(`🚀 开始执行创维会员中心签到...`);
            const signinSuccess = await this.performSignin();

            if (signinSuccess) {
                console.log(`✅ [${this.wxid}] 签到完成`);
                // 更新缓存
                this.saveTokenCache();
            } else {
                console.log(`❌ [${this.wxid}] 签到失败`);
                print(`[${this.wxid}] 签到失败`, true);
            }
        } catch (error) {
            console.log(`[${this.wxid}] 脚本执行出错：${error.message}`);
            print(`[${this.wxid}] 脚本执行出错：${error.message}`, true);
            if (isDebug) {
                console.error(error);
            }
        }
    }
}

// 主函数
async function main() {
    console.log(`🔔 创维会员中心签到脚本开始执行`);

    if (isDebug) {
        console.log(`[DEBUG] 调试模式已开启`);
        console.log(`[DEBUG] APPID: ${APPID}`);
        console.log(`[DEBUG] 签到活动ID: ${new SkyworthSignin('').skyworthConfig.signOperatingId}`);
    }
    
    if (!wxidList) {
        console.log(`❌ 未设置环境变量 TXX_WXID 或命令行参数 --wxid`);
        return;
    }

    // 处理单个wxid或多个wxid
    const wxids = cmdWxid ? [cmdWxid] : parseWxidList(wxidList);

    if (wxids.length === 0) {
        console.log(`❌ 没有找到有效的wxid`);
        return;
    }

    console.log(`📋 共找到 ${wxids.length} 个有效账号`);

    if (isDebug) {
        console.log(`[DEBUG] 账号列表: ${wxids.join(', ')}`);
    }

    // 逐个处理账号
    for (let i = 0; i < wxids.length; i++) {
        const wxid = wxids[i];
        console.log(`\n🚀 [${i + 1}/${wxids.length}] 开始处理账号: ${wxid}`);

        try {
            const script = new SkyworthSignin(wxid);
            await script.run();
            console.log(`✅ [${i + 1}/${wxids.length}] 账号 ${wxid} 处理完成`);
        } catch (error) {
            console.log(`❌ [${i + 1}/${wxids.length}] 账号 ${wxid} 处理失败: ${error.message}`);
            if (isDebug) {
                console.error(error);
            }
        }

        console.log('─'.repeat(60));

        // 如果不是最后一个账号，稍微延迟一下
        if (i < wxids.length - 1) {
            await new Promise(resolve => setTimeout(resolve, 1000));
        }
    }

    console.log(`\n🎉 所有账号处理完成！`);

    // 发送通知
    if (NOTICE_SWITCH && notice) {
        await sendMsg(notice);
    }
}

// 通知相关变量和函数
let notice = '';

function print(msg, is_notice = false) {
    let str = `${msg}`;
    console.log(str);
    if (NOTICE_SWITCH && is_notice) {
        notice += `${str}\n`;
    }
}

async function sendMsg(message) {
    try {
        let notify = '';
        try {
            notify = require('./sendNotify');
        } catch (e) {
            try {
                notify = require("../sendNotify");
            } catch (e2) {
                console.log('❌ 未找到sendNotify模块，无法发送通知');
                return;
            }
        }
        await notify.sendNotify(scriptName, message);
        console.log('📢 通知发送成功');
    } catch (error) {
        console.log(`❌ 通知发送失败: ${error.message}`);
    }
}

// 执行脚本
main().catch(console.error);
