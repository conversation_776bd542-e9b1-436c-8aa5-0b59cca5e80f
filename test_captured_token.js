/**
 * 用抓包数据验证token解析方法
 */

console.log('=== 用抓包数据验证token解析 ===\n');

// 从抓包文件中提取的实际混淆代码
const CAPTURED_OBFUSCATED_CODE = `/*qNVOMRa*/var/*RyR687XEI83CJb8fHR*/__qxBY2hC/*YMXkbo*/=\\u0053\\u0074\\u0072\\u0069\\u006e\\u0067
/*lXX1kbkRXJ9FmZqK*/./*MZCeD*/\\u0066r\\u006fm\\u0043ha\\u0072C\\u006fde/*sMmtyNpcAyql8O*/;
var/*Cfhx2UMXDK6BMZM*/_x_sEd = [/*dF4tvpacOzsnV9*/2212,53,2852,3445,3327,/*SPaJmEBdtdDh0xIQ5b*/];//vYnpXz6vIEdS5RW
var/*bcNoFOVIxVdidN*/_$3vge/*TLSE8sLsN11d*/=/*WloJg5inrhui0kzNK*/function(/*IzKb6MJKvAxCI*/){
/*4XMcxf8q6w5Eu9E8*/return/*tjsXXWm*/arguments[/*7KLqBRABhc6cioDsfg*/0]^/*NkiyNuHr*/
/*PX3neKSxLULiZ3uE*/_x_sEd[/*oZo7hG3hKugxQh6c5Sh*/0];/*Gy6Av9AnY5HTsFbp*/}/*QeINbNufqrnfY*/;
var/*v7zHapXuogfPBTAN*/_$89tQ/*UvaafX*/=/*3ns72qDDUBjuskBpx*/function(/*SBWW1CojExH8wpLEWg*/){
/*Rlccoq6ahe7EFcB4*/return/*WUb0GTgF*/arguments[/*P57JRWMXxW*/0]^/*wuGFWfO1*/
/*hrzhhgoyU*/_x_sEd[/*1eNOTd8mMjatIcEC*/1];/*QCKgolYTh*/}/*jKtj2G0Ls2TcH*/;
var/*SIFKkOuBNUnJlDrM*/_$2ez/*yfzoPOI8Fib00OELrm2*/=/*9w8TNqsIoLCi*/function(/*iN7pPA*/){
/*NRSvRnrefQJGwlSKF*/return/*hlpVATricR4QI*/arguments[/*TKhZPT*/0]^/*DcyqzBmfWtYZtrYq*/
/*ephneKlI5Klj3XaY*/_x_sEd[/*xQwiYQhHAIcVBl*/2];/*VaVwcP0KsBCb*/}/*O2AbLE3*/;
var/*EBCmqPRXJjw963BUW*/_$zcp/*FIxtMxvs7*/=/*hiQYBLvx4Q*/function(/*blmZnKgp9lNT*/){
/*OpFi3sgXXE4yRwWQtmg*/return/*UyuXTyv3kqhAm*/arguments[/*HOROTkaqm0n*/0]^/*YVuXf7JfY*/
/*dHvhIDpEqH*/_x_sEd[/*u3RnTzJIop*/3];/*dp2w9GwOVNCg6N*/}/*skQDPME88ahZdMrfaO*/;
var/*ZUvScLDkqPit2O*/_$mRym/*KPYaqzLzv1*/=/*RyJFl*/function(/*vj7hxeNNgVoNSQRv*/){
/*FpAZa7H*/return/*CLoUkWelj8eJ*/arguments[/*oQdReQyvmxXvuP*/0]^/*ExXATHEo*/
/*oUPGWDQRtI0aOZS*/_x_sEd[/*iqgXP7CzR0*/4];/*GPHHeemz7ZD54*/}/*jg0tDHN72hX*/;`;

// 抓包数据中的实际token
const EXPECTED_TOKEN = '7jdcpm';

console.log(`期望的token: ${EXPECTED_TOKEN}`);
console.log(`抓包代码长度: ${CAPTURED_OBFUSCATED_CODE.length}\n`);

// 使用我们的VM方法解析抓包代码
function testVMMethod() {
    console.log('1. 测试VM解析方法:');
    
    try {
        const vm = require('vm');
        let extractedToken = null;
        let allWindowProps = {};
        
        // 创建沙盒环境
        const sandbox = {
            String: String,
            Math: Math,
            parseInt: parseInt,
            parseFloat: parseFloat,
            window: {},
            console: {
                log: (...args) => {
                    console.log('VM输出:', ...args);
                    args.forEach(arg => {
                        if (typeof arg === 'string' && /^[a-z0-9]{6,12}$/.test(arg)) {
                            extractedToken = arg;
                            console.log(`🎯 捕获到可能的token: ${arg}`);
                        }
                    });
                }
            },
            eval: (code) => {
                try {
                    console.log(`执行eval代码: ${code.substring(0, 200)}...`);
                    const result = vm.runInContext(code, sandbox, { timeout: 5000 });
                    
                    // 检查window对象中的所有属性
                    if (sandbox.window) {
                        Object.keys(sandbox.window).forEach(key => {
                            const value = sandbox.window[key];
                            allWindowProps[key] = value;
                            if (typeof value === 'string' && /^[a-z0-9]{6,12}$/.test(value)) {
                                extractedToken = value;
                                console.log(`从window.${key}获取到token: ${value}`);
                            }
                        });
                    }
                    
                    return result;
                } catch (e) {
                    console.log(`eval执行失败: ${e.message}`);
                    return undefined;
                }
            }
        };
        
        vm.createContext(sandbox);
        
        // 执行抓包的混淆代码
        console.log('执行抓包混淆代码...');
        vm.runInContext(CAPTURED_OBFUSCATED_CODE, sandbox, {
            timeout: 10000,
            displayErrors: true
        });
        
        console.log(`\nVM解析结果:`);
        console.log(`- 提取的token: ${extractedToken}`);
        console.log(`- window属性数量: ${Object.keys(allWindowProps).length}`);
        console.log(`- 匹配期望token: ${extractedToken === EXPECTED_TOKEN ? '✅' : '❌'}`);
        
        if (Object.keys(allWindowProps).length > 0) {
            console.log('\n所有window属性:');
            Object.entries(allWindowProps).forEach(([key, value]) => {
                console.log(`  ${key}: ${value}`);
            });
        }
        
        return extractedToken;
        
    } catch (error) {
        console.log(`VM方法失败: ${error.message}`);
        return null;
    }
}

// 手动解析抓包代码的结构
function analyzeStructure() {
    console.log('\n2. 分析抓包代码结构:');
    
    // 提取XOR数组
    const arrayMatch = CAPTURED_OBFUSCATED_CODE.match(/_x_sEd\s*=\s*\[([^\]]+)\]/);
    let xorArray = [];
    
    if (arrayMatch && arrayMatch[1]) {
        const arrayStr = arrayMatch[1].replace(/\/\*[^*]*\*\//g, '').replace(/\s+/g, '');
        xorArray = arrayStr.split(',').map(s => parseInt(s.trim())).filter(n => !isNaN(n));
        console.log(`XOR数组: [${xorArray.join(', ')}]`);
    }
    
    // 检查是否有eval调用
    const hasEval = CAPTURED_OBFUSCATED_CODE.includes('\\u0065\\u0076\\u0061\\u006c');
    console.log(`包含eval调用: ${hasEval}`);
    
    // 检查函数定义
    const funcMatches = CAPTURED_OBFUSCATED_CODE.match(/var[^=]*_\$\w+[^=]*=\s*function/g);
    console.log(`XOR函数数量: ${funcMatches ? funcMatches.length : 0}`);
    
    return { xorArray, hasEval, funcCount: funcMatches ? funcMatches.length : 0 };
}

// 尝试手动构建执行环境
function manualExecution() {
    console.log('\n3. 手动执行测试:');
    
    try {
        // 构建执行环境
        const _x_sEd = [2212, 53, 2852, 3445, 3327];
        const __qxBY2hC = String.fromCharCode;
        
        const _$3vge = (x) => x ^ _x_sEd[0];
        const _$89tQ = (x) => x ^ _x_sEd[1];
        const _$2ez = (x) => x ^ _x_sEd[2];
        const _$zcp = (x) => x ^ _x_sEd[3];
        const _$mRym = (x) => x ^ _x_sEd[4];
        
        console.log('构建的执行环境:');
        console.log(`_x_sEd: [${_x_sEd.join(', ')}]`);
        console.log(`__qxBY2hC: String.fromCharCode`);
        
        // 测试一些XOR函数
        console.log('\n测试XOR函数:');
        const testValues = [100, 200, 300, 400, 500];
        testValues.forEach(val => {
            const results = [
                _$3vge(val),
                _$89tQ(val),
                _$2ez(val),
                _$zcp(val),
                _$mRym(val)
            ];
            console.log(`输入${val}: [${results.join(', ')}]`);
            
            // 检查是否有结果对应期望token的字符
            const chars = results.map(r => r >= 32 && r <= 126 ? String.fromCharCode(r) : '?');
            console.log(`  字符: [${chars.join(', ')}]`);
        });
        
        // 尝试找到生成期望token的输入值
        console.log('\n寻找生成期望token的输入值:');
        const targetASCII = EXPECTED_TOKEN.split('').map(c => c.charCodeAt(0));
        console.log(`期望ASCII: [${targetASCII.join(', ')}]`);
        
        targetASCII.forEach((ascii, index) => {
            console.log(`\n字符'${EXPECTED_TOKEN[index]}' (ASCII ${ascii}):`);
            
            // 尝试每个XOR函数
            [_$3vge, _$89tQ, _$2ez, _$zcp, _$mRym].forEach((func, funcIndex) => {
                // 寻找输入值: input ^ xor_value = ascii
                // 即: input = ascii ^ xor_value
                const input = ascii ^ _x_sEd[funcIndex];
                const result = func(input);
                
                if (result === ascii) {
                    console.log(`  ✅ XOR函数${funcIndex}(${input}) = ${result} = '${String.fromCharCode(result)}'`);
                } else {
                    console.log(`  ❌ XOR函数${funcIndex}(${input}) = ${result} ≠ ${ascii}`);
                }
            });
        });
        
    } catch (error) {
        console.log(`手动执行失败: ${error.message}`);
    }
}

// 主测试函数
function main() {
    const structure = analyzeStructure();
    const vmToken = testVMMethod();
    manualExecution();
    
    console.log('\n=== 测试结果总结 ===');
    console.log(`期望token: ${EXPECTED_TOKEN}`);
    console.log(`VM解析token: ${vmToken || 'null'}`);
    console.log(`解析成功: ${vmToken === EXPECTED_TOKEN ? '✅' : '❌'}`);
    
    if (vmToken !== EXPECTED_TOKEN) {
        console.log('\n❌ 解析失败的可能原因:');
        console.log('1. 抓包代码不完整');
        console.log('2. eval参数解析错误');
        console.log('3. 缺少关键的执行上下文');
        console.log('4. token生成依赖其他参数');
    } else {
        console.log('\n✅ 解析成功！可以应用到主脚本中。');
    }
}

// 运行测试
main();
