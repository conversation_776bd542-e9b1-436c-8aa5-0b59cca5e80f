from urllib.parse import urlparse, parse_qs, unquote
import json

test_url = "https://mcs-mimp-web.sf-express.com/mcs-mimp/share/weChat/activityRedirect?source=CX&unionId=YQ1N3lUDNaSg9Ox9%2BBwNXRFFsW2OBAKilgqhG8pgOBo%3D&openId=s1G5ik6AWFIQQaL5sshp%2FZwTIdtITonvNlva7jmu380%3D&memId=hych5uZiPmTZK%2FpoxrKOmetUH%2BV8uGn%2F%2FYU88EDDho4DdjSr%2F6X0WoiZtgGzs7sG&memNo=6tB15T6k0ZvyroDMcHJm6TxGNOheCN75HuG7rOc5Fw4DdjSr%2F6X0WoiZtgGzs7sG&mobile=EpJ84tf34s%2B0ot4Y2kXwuA%3D%3D&mediaCode=wxapp&bizCode=%7B%22path%22%3A%22%2Fup-member%2FnewHome%22%2C%22supportShare%22%3A%22NO%22%2C%22maskType%22%3A%22autoReceive%22%2C%22from%22%3A%22surprise_benefitwxauto%22%2C%22equityKey%22%3A%22surprise_benefit%22%7D&citycode=833&cityname=%E4%B9%90%E5%B1%B1&cx-at-sign=C6E170041AF7782DC61DDEDD46E8C24CA3A0D1D4AD24CD5D0505763F8F4E0FA7&cx-at-ts=1754409196&cx-at-nonce=qgAzD0Sotk60w19Okw3CV&t=1754409196"

parsed_url = urlparse(test_url)
params = parse_qs(parsed_url.query)

print("URL参数分析:")
for key, value in params.items():
    if key == 'bizCode':
        decoded = unquote(value[0])
        print(f"{key}: {decoded}")
    elif key == 'cityname':
        decoded = unquote(value[0])
        print(f"{key}: {decoded}")
    else:
        print(f"{key}: {value[0]}")
