/**
 * 验证算法正确性 - 使用抓包数据
 */

// 从抓包数据中提取的实际混淆JavaScript代码
const actualTokenScript = `/*qNVOMRa*/var/*RyR687XEI83CJb8fHR*/__qxBY2hC/*YMXkbo*/=\\u0053\\u0074\\u0072\\u0069\\u006e\\u0067
/*lXX1kbkRXJ9FmZqK*/./*MZCeD*/\\u0066r\\u006fm\\u0043ha\\u0072C\\u006fde/*sMmtyNpcAyql8O*/;
var/*Cfhx2UMXDK6BMZM*/_x_sEd = [/*dF4tvpacOzsnV9*/2212,53,2852,3445,3327,/*SPaJmEBdtdDh0xIQ5b*/];//vYnpXz6vIEdS5RW
var/*bcNoFOVIxVdidN*/_$3vge/*TLSE8sLsN11d*/=/*WloJg5inrhui0kzNK*/function(/*IzKb6MJKvAxCI*/){
/*4XMcxf8q6w5Eu9E8*/return/*tjsXXWm*/arguments[/*7KLqBRABhc6cioDsfg*/0]^/*NkiyNuHr*/
/*PX3neKSxLULiZ3uE*/_x_sEd[/*oZo7hG3hKugxQh6c5Sh*/0];/*Gy6Av9AnY5HTsFbp*/}/*QeINbNufqrnfY*/;`;

// 抓包数据
const ACTUAL_TIMESTAMP = 1754383264519;
const ACTUAL_TOKEN = '7jdcpm';
const SIGN_TIMESTAMP = 1754383264725;

console.log('=== 抓包数据验证 ===');
console.log(`Token获取时间戳: ${ACTUAL_TIMESTAMP}`);
console.log(`实际生成token: ${ACTUAL_TOKEN}`);
console.log(`签到时间戳: ${SIGN_TIMESTAMP}`);
console.log(`时间差: ${SIGN_TIMESTAMP - ACTUAL_TIMESTAMP}ms\n`);

// 简单哈希函数
function simpleHash(str) {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
        const char = str.charCodeAt(i);
        hash = ((hash << 5) - hash) + char;
        hash = hash & hash;
    }
    return Math.abs(hash);
}

// 我们的算法
function parseObfuscatedToken(tokenScript, timestamp) {
    try {
        console.log(`\n=== 算法执行过程 ===`);
        console.log(`输入时间戳: ${timestamp}`);
        
        // 1. 提取XOR数组
        const arrayMatch = tokenScript.match(/_x_sEd\s*=\s*\[([^\]]+)\]/);
        let xorArray = [2212, 53, 2852, 3445, 3327];
        
        if (arrayMatch && arrayMatch[1]) {
            const arrayStr = arrayMatch[1].replace(/\/\*[^*]*\*\//g, '').replace(/\s+/g, '');
            const numbers = arrayStr.split(',').map(s => {
                const trimmed = s.trim();
                const num = parseInt(trimmed);
                return isNaN(num) ? parseInt(trimmed, 16) || 0 : num;
            });
            if (numbers.length >= 5) {
                xorArray = numbers.slice(0, 5);
            }
        }
        console.log(`XOR数组: [${xorArray.join(', ')}]`);

        // 2. 时间戳处理
        const timeStr = timestamp.toString();
        const timeHash = simpleHash(timeStr);
        console.log(`时间戳字符串: ${timeStr}`);
        console.log(`时间戳哈希: ${timeHash}`);
        
        // 3. 生成token
        const chars = 'abcdefghijklmnopqrstuvwxyz0123456789';
        let token = '';
        
        console.log(`\n字符集: ${chars}`);
        console.log(`字符集长度: ${chars.length}`);
        console.log(`\n逐位计算:`);
        
        for (let i = 0; i < 6; i++) {
            const xorValue = xorArray[i % xorArray.length];
            const timeValue = parseInt(timeStr.charAt(i % timeStr.length)) || 0;
            const hashValue = (timeHash + i) % chars.length;
            const index = (xorValue ^ timeValue ^ hashValue) % chars.length;
            const char = chars[index];
            
            console.log(`位置${i}: xor=${xorValue}, time=${timeValue}, hash=${hashValue}, index=${index}, char='${char}'`);
            token += char;
        }
        
        console.log(`\n生成的token: ${token}`);
        return token;
        
    } catch (error) {
        console.log(`算法执行失败: ${error.message}`);
        return null;
    }
}

// 尝试VM执行原始混淆代码
function tryVMExecution(tokenScript) {
    try {
        console.log(`\n=== VM执行混淆代码 ===`);
        
        const vm = require('vm');
        let capturedToken = null;
        
        // 创建执行环境
        const context = {
            String: String,
            Math: Math,
            parseInt: parseInt,
            parseFloat: parseFloat,
            console: {
                log: function(...args) {
                    console.log('VM输出:', ...args);
                    args.forEach(arg => {
                        if (typeof arg === 'string' && /^[a-z0-9]{6,12}$/.test(arg)) {
                            capturedToken = arg;
                            console.log(`捕获到可能的token: ${arg}`);
                        }
                    });
                }
            },
            eval: function(code) {
                try {
                    const result = vm.runInContext(code, context, { timeout: 5000 });
                    if (typeof result === 'string' && /^[a-z0-9]{6,12}$/.test(result)) {
                        capturedToken = result;
                        console.log(`eval返回可能的token: ${result}`);
                    }
                    return result;
                } catch (e) {
                    console.log(`eval执行失败: ${e.message}`);
                    return undefined;
                }
            }
        };

        vm.createContext(context);
        
        console.log(`执行混淆代码...`);
        const result = vm.runInContext(tokenScript, context, {
            timeout: 10000,
            displayErrors: false
        });

        console.log(`VM执行结果类型: ${typeof result}`);
        console.log(`VM执行结果: ${result}`);
        
        if (typeof result === 'string' && /^[a-z0-9]{6,12}$/.test(result)) {
            capturedToken = result;
        }
        
        return capturedToken;
        
    } catch (error) {
        console.log(`VM执行失败: ${error.message}`);
        return null;
    }
}

// 测试不同的算法变体
function testAlgorithmVariants(timestamp) {
    console.log(`\n=== 测试算法变体 ===`);
    
    const variants = [
        // 变体1: 原始算法
        (ts) => parseObfuscatedToken(actualTokenScript, ts),
        
        // 变体2: 使用签到时间戳
        (ts) => parseObfuscatedToken(actualTokenScript, SIGN_TIMESTAMP),
        
        // 变体3: 简化算法
        (ts) => {
            const chars = 'abcdefghijklmnopqrstuvwxyz0123456789';
            const timeStr = ts.toString();
            let token = '';
            for (let i = 0; i < 6; i++) {
                const index = (parseInt(timeStr.charAt(i % timeStr.length)) * (i + 1) + ts % 1000) % chars.length;
                token += chars[index];
            }
            return token;
        },
        
        // 变体4: 基于MD5
        (ts) => {
            const crypto = require('crypto');
            const hash = crypto.createHash('md5').update(ts.toString()).digest('hex');
            return hash.substring(0, 6);
        }
    ];
    
    variants.forEach((variant, index) => {
        try {
            const result = variant(timestamp);
            console.log(`变体${index + 1}: ${result} ${result === ACTUAL_TOKEN ? '✅ 匹配!' : ''}`);
        } catch (error) {
            console.log(`变体${index + 1}: 执行失败 - ${error.message}`);
        }
    });
}

// 主测试函数
function main() {
    console.log('开始验证算法...\n');
    
    // 1. 使用我们的算法
    const ourToken = parseObfuscatedToken(actualTokenScript, ACTUAL_TIMESTAMP);
    
    // 2. 尝试VM执行
    const vmToken = tryVMExecution(actualTokenScript);
    
    // 3. 测试算法变体
    testAlgorithmVariants(ACTUAL_TIMESTAMP);
    
    // 4. 结果对比
    console.log(`\n=== 结果对比 ===`);
    console.log(`实际token:    ${ACTUAL_TOKEN}`);
    console.log(`我们的算法:   ${ourToken} ${ourToken === ACTUAL_TOKEN ? '✅' : '❌'}`);
    console.log(`VM执行结果:   ${vmToken || 'null'} ${vmToken === ACTUAL_TOKEN ? '✅' : '❌'}`);
    
    // 5. 时间戳相关性测试
    console.log(`\n=== 时间戳相关性测试 ===`);
    const nearbyTimestamps = [
        ACTUAL_TIMESTAMP - 1000,
        ACTUAL_TIMESTAMP,
        ACTUAL_TIMESTAMP + 1000,
        SIGN_TIMESTAMP
    ];
    
    nearbyTimestamps.forEach(ts => {
        const token = parseObfuscatedToken(actualTokenScript, ts);
        const isActual = ts === ACTUAL_TIMESTAMP;
        const isSign = ts === SIGN_TIMESTAMP;
        console.log(`${ts} ${isActual ? '(token获取)' : isSign ? '(签到)' : ''}: ${token} ${token === ACTUAL_TOKEN ? '✅' : ''}`);
    });
}

// 运行测试
main();
