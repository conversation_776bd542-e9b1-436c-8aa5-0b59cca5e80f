GET /miniprogram/api/v1/get-user HTTP/1.1
Host: uc-api.skyallhere.com
Connection: keep-alive
authorization: Bearer eyJhb<PERSON>ciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJvcGVuaWQiOiJvTVNTZzRnbXBwOWp5ajJaS2F4OGc3TmhsUEJJIiwidW5pb25pZCI6Im9BdWVHamp0T1hUQmRuS2lhUDk2U0VUcUZiNTQiLCJ1c2VyaWQiOjE5NDYwMzk3LCJ1c2VyX2NvZGUiOiI0MmQ0MDY2MGRkOTIyOGMwIiwidXNlcl9waG9uZSI6IjE4MTExNDU1NjI3Iiwibmlja19uYW1lIjoiIiwiZXhwIjoxNzU0Mzg0OTc3fQ.DGY4VsOt0lKbkUf_3FzWTDl1_OuUtKlttbxTadzn9eU
charset: utf-8
app-model: M2012K11AC
app-system: Android 13
app-path: /pages/user/user
User-Agent: Mozilla/5.0 (Linux; Android 13; M2012K11AC Build/TKQ1.221114.001; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/138.0.7204.180 Mobile Safari/537.36 XWEB/1380085 MMWEBSDK/20240404 MMWEBID/4824 MicroMessenger/8.0.49.2600(0x28003133) WeChat/arm64 Weixin NetType/WIFI Language/zh_CN ABI/arm64 MiniProgramEnv/android
content-type: application/json
Accept-Encoding: gzip,compress,br,deflate
app-sdkversion: 3.4.10
app-version: 8.0.49
Referer: https://servicewechat.com/wxff438d3c60c63fb6/342/page-frame.html



HTTP/1.1 200 OK
Date: Tue, 05 Aug 2025 08:40:59 GMT
Content-Type: application/json; charset=utf-8
Content-Length: 1070
Connection: keep-alive
X-Request-Id: 6966b8baf383cd823155c6a5a3dfff10
Access-Control-Allow-Origin: *
Access-Control-Allow-Credentials: true

{"code":0,"data":{"baseInfo":{"address":"","birthday":null,"city":"","county":"","growthValue":320,"headImg":"https://obs-yonghuyunying.obs.cn-south-1.myhuaweicloud.com/member-center/static/avatar-default.png","inviteTag":"42d40660dd9228c0","latitude":"","longitude":"","nickName":"微信用户","openid":"oMSSg4gmpp9jyj2ZKax8g7NhlPBI","phone":"18111455627","province":"","todayGrowth":0,"todayScore":0,"unionid":"oAueGjjtOXTBdnKiaP96SETqFb54","userGender":0,"userGrade":1,"userScore":305,"vipLevel":1},"extra":{"prizePage":"https://uc-api.skyallhere.com/miniprogram/tp/duiba-nologin?dbredirect=%2F%2F74367.activity-13.m.duiba.com.cn%2Fcrecord%2Frecord%3Fdbnewopen"},"memberGrade":[{"levelGrade":1,"levelName":"基础","max":999,"min":1},{"levelGrade":2,"levelName":"白银","max":11999,"min":1000},{"levelGrade":3,"levelName":"黄金","max":24999,"min":12000},{"levelGrade":4,"levelName":"钻石","max":75999,"min":25000},{"levelGrade":5,"levelName":"黑金","max":-1,"min":76000}],"product":[],"taskProgress":{"isCanUploadInvoice":true,"isImproved":false}},"msg":"ok"}