/**
 * 简单Token测试脚本
 */

// 简单哈希函数
function simpleHash(str) {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
        const char = str.charCodeAt(i);
        hash = ((hash << 5) - hash) + char;
        hash = hash & hash;
    }
    return Math.abs(hash);
}

// Token生成函数
function generateToken(timestamp) {
    const xorArray = [2212, 53, 2852, 3445, 3327];
    const timeStr = timestamp.toString();
    const timeHash = simpleHash(timeStr);
    const chars = 'abcdefghijklmnopqrstuvwxyz0123456789';
    let token = '';
    
    for (let i = 0; i < 6; i++) {
        const xorValue = xorArray[i % xorArray.length];
        const timeValue = parseInt(timeStr.charAt(i % timeStr.length)) || 0;
        const hashValue = (timeHash + i) % chars.length;
        const index = (xorValue ^ timeValue ^ hashValue) % chars.length;
        token += chars[index];
    }
    
    return token;
}

console.log('=== 简单Token测试 ===');

// 测试1: 复现性
const timestamp1 = 1754383264519;
const token1a = generateToken(timestamp1);
const token1b = generateToken(timestamp1);

console.log(`时间戳: ${timestamp1}`);
console.log(`第一次: ${token1a}`);
console.log(`第二次: ${token1b}`);
console.log(`复现性: ${token1a === token1b ? '✅' : '❌'}`);

// 测试2: 不同时间戳
const timestamp2 = 1754383264520;
const token2 = generateToken(timestamp2);

console.log(`\n不同时间戳: ${timestamp2}`);
console.log(`生成token: ${token2}`);
console.log(`是否不同: ${token1a !== token2 ? '✅' : '❌'}`);

// 测试3: 当前时间
const now = Date.now();
const tokenNow = generateToken(now);

console.log(`\n当前时间: ${now}`);
console.log(`当前token: ${tokenNow}`);

console.log('\n测试完成！');
