GET /member-center/data/privilege.json HTTP/1.1
Host: obs-yonghuyunying.obs.cn-south-1.myhuaweicloud.com
Connection: keep-alive
xweb_xhr: 1
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF WindowsWechat(0x63090c37)XWEB/14315
Content-Type: application/json
Accept: */*
Sec-Fetch-Site: cross-site
Sec-Fetch-Mode: cors
Sec-Fetch-Dest: empty
Referer: https://servicewechat.com/wxff438d3c60c63fb6/342/page-frame.html
Accept-Encoding: gzip, deflate, br
Accept-Language: zh-CN,zh;q=0.9



HTTP/1.1 200 OK
Server: OBS
Date: Tu<PERSON>, 05 Aug 2025 06:18:49 GMT
Content-Type: application/json
Content-Length: 6984
Connection: keep-alive
x-obs-request-id: 0000019878E1F0A1964666EF3120E254
Accept-Ranges: bytes
ETag: "12da15b09f885a80574ff29fed8d1610"
Last-Modified: Fri, 25 Dec 2020 02:52:39 GMT
x-obs-tagging-count: 0
x-obs-id-2: 36AAAQAAEAABAAAQAAEAABAAAQAAEAABAAAaI=AAAAAAAAAAAAAAAAAAAAAAAAAA

[
	{
		"id": "movies",
		"img": "https://obs-yonghuyunying.obs.cn-south-1.myhuaweicloud.com/member-center/static/privilege-movies.png",
		"shortName": "影视",
		"name": "影视权益",
		"banner": "https://obs-yonghuyunying.obs.cn-south-1.myhuaweicloud.com/member-center/static/privilege-movies-banner.png",
		"detail": [
			{
				"name": "青铜",
				"icon": "https://obs-yonghuyunying.obs.cn-south-1.myhuaweicloud.com/member-center/static/grade-badge-1.png",
				"subname": "领优酷酷喵会员58折特惠券"
			},
			{
				"name": "白银",
				"icon": "https://obs-yonghuyunying.obs.cn-south-1.myhuaweicloud.com/member-center/static/grade-badge-2.png",
				"subname": "领优酷酷喵会员58折特惠券/芒果TV会员55折大促券"
			},
			{
				"name": "黄金",
				"icon": "https://obs-yonghuyunying.obs.cn-south-1.myhuaweicloud.com/member-center/static/grade-badge-3.png",
				"subname": "领优酷酷喵会员58折特惠券/芒果TV会员55折大促券/优酷VIP全场68折"
			},
			{
				"name": "铂金",
				"icon": "https://obs-yonghuyunying.obs.cn-south-1.myhuaweicloud.com/member-center/static/grade-badge-4.png",
				"subname": "领优酷酷喵会员58折特惠券/芒果TV会员55折大促券/优酷VIP全场68折/腾讯超级影视会员68折/腾讯视频会员78折特惠券"
			},
			{
				"name": "钻石",
				"icon": "https://obs-yonghuyunying.obs.cn-south-1.myhuaweicloud.com/member-center/static/grade-badge-5.png",
				"subname": "领优酷酷喵会员58折特惠券/芒果TV会员55折大促券/优酷VIP全场68折/腾讯超级影视会员68折/腾讯视频会员78折特惠券"
			},
			{
				"name": "黑金",
				"icon": "https://obs-yonghuyunying.obs.cn-south-1.myhuaweicloud.com/member-center/static/grade-badge-6.png",
				"subname": "领优酷酷喵会员58折特惠券/芒果TV会员55折大促券/优酷VIP全场68折/腾讯超级影视会员68折/腾讯视频会员78折特惠券"
			}
		]
	},
	{
		"id": "eat",
		"img": "https://obs-yonghuyunying.obs.cn-south-1.myhuaweicloud.com/member-center/static/privilege-eat.png",
		"shortName": "吃喝",
		"name": "吃喝权益",
		"banner": "https://obs-yonghuyunying.obs.cn-south-1.myhuaweicloud.com/member-center/static/privilege-eat-banner.png",
		"detail": [
			{
				"name": "青铜",
				"icon": "https://obs-yonghuyunying.obs.cn-south-1.myhuaweicloud.com/member-center/static/grade-badge-1.png",
				"subname": "领幸福西饼满99减30元、198减50元券"
			},
			{
				"name": "白银",
				"icon": "https://obs-yonghuyunying.obs.cn-south-1.myhuaweicloud.com/member-center/static/grade-badge-2.png",
				"subname": "领幸福西饼满99减30元、198减50元券"
			},
			{
				"name": "黄金",
				"icon": "https://obs-yonghuyunying.obs.cn-south-1.myhuaweicloud.com/member-center/static/grade-badge-3.png",
				"subname": "领幸福西饼满99减30元、198减50元券/真功夫7元商品券"
			},
			{
				"name": "铂金",
				"icon": "https://obs-yonghuyunying.obs.cn-south-1.myhuaweicloud.com/member-center/static/grade-badge-4.png",
				"subname": "领幸福西饼满99减30元、198减50元券/真功夫9元商品券、1分购百事可乐"
			},
			{
				"name": "钻石",
				"icon": "https://obs-yonghuyunying.obs.cn-south-1.myhuaweicloud.com/member-center/static/grade-badge-5.png",
				"subname": "领幸福西饼满99减30元、198减50元券/真功夫9元商品券、1分购百事可乐"
			},
			{
				"name": "黑金",
				"icon": "https://obs-yonghuyunying.obs.cn-south-1.myhuaweicloud.com/member-center/static/grade-badge-6.png",
				"subname": "领幸福西饼满99减30元、198减50元券/真功夫9元商品券、1分购百事可乐等"
			}
		]
	},
	{
		"id": "life",
		"img": "https://obs-yonghuyunying.obs.cn-south-1.myhuaweicloud.com/member-center/static/privilege-life.png",
		"shortName": "生活",
		"name": "生活权益",
		"banner": "https://obs-yonghuyunying.obs.cn-south-1.myhuaweicloud.com/member-center/static/privilege-life-banner.png",
		"detail": [
			{
				"name": "青铜",
				"icon": "https://obs-yonghuyunying.obs.cn-south-1.myhuaweicloud.com/member-center/static/grade-badge-1.png",
				"subname": "领喜马拉雅会员6折特惠券"
			},
			{
				"name": "白银",
				"icon": "https://obs-yonghuyunying.obs.cn-south-1.myhuaweicloud.com/member-center/static/grade-badge-2.png",
				"subname": "领喜马拉雅会员6折特惠券/亚朵生活商城100元券"
			},
			{
				"name": "黄金",
				"icon": "https://obs-yonghuyunying.obs.cn-south-1.myhuaweicloud.com/member-center/static/grade-badge-3.png",
				"subname": "领喜马拉雅会员6折特惠券/亚朵生活商城100元券/网易云音乐会员75折券"
			},
			{
				"name": "铂金",
				"icon": "https://obs-yonghuyunying.obs.cn-south-1.myhuaweicloud.com/member-center/static/grade-badge-4.png",
				"subname": "领喜马拉雅会员6折特惠券/亚朵生活商城100元券/网易云音乐会员75折券/君亭酒店集团铂金卡"
			},
			{
				"name": "钻石",
				"icon": "https://obs-yonghuyunying.obs.cn-south-1.myhuaweicloud.com/member-center/static/grade-badge-5.png",
				"subname": "领喜马拉雅会员6折特惠券/亚朵生活商城100元券/网易云音乐会员75折券/君亭酒店集团铂金卡/爱康体检宝专享优惠"
			},
			{
				"name": "黑金",
				"icon": "https://obs-yonghuyunying.obs.cn-south-1.myhuaweicloud.com/member-center/static/grade-badge-6.png",
				"subname": "领喜马拉雅会员6折特惠券/亚朵生活商城100元券/网易云音乐会员75折券/君亭酒店集团铂金卡/爱康体检宝专享优惠"
			}
		]
	},
	{
		"id": "upgrade",
		"img": "https://obs-yonghuyunying.obs.cn-south-1.myhuaweicloud.com/member-center/static/privilege-upgrade.png",
		"shortName": "升级",
		"name": "升级奖励",
		"banner": "https://obs-yonghuyunying.obs.cn-south-1.myhuaweicloud.com/member-center/static/privilege-upgrade-banner.png",
		"lock": 1,
		"detail": [
			{
				"name": "青铜",
				"icon": "https://obs-yonghuyunying.obs.cn-south-1.myhuaweicloud.com/member-center/static/grade-badge-1.png",
				"subname": "无"
			},
			{
				"name": "白银",
				"icon": "https://obs-yonghuyunying.obs.cn-south-1.myhuaweicloud.com/member-center/static/grade-badge-2.png",
				"subname": "送300积分"
			},
			{
				"name": "黄金",
				"icon": "https://obs-yonghuyunying.obs.cn-south-1.myhuaweicloud.com/member-center/static/grade-badge-3.png",
				"subname": "送500积分"
			},
			{
				"name": "铂金",
				"icon": "https://obs-yonghuyunying.obs.cn-south-1.myhuaweicloud.com/member-center/static/grade-badge-4.png",
				"subname": "送1000积分"
			},
			{
				"name": "钻石",
				"icon": "https://obs-yonghuyunying.obs.cn-south-1.myhuaweicloud.com/member-center/static/grade-badge-5.png",
				"subname": "送2000积分"
			},
			{
				"name": "黑金",
				"icon": "https://obs-yonghuyunying.obs.cn-south-1.myhuaweicloud.com/member-center/static/grade-badge-6.png",
				"subname": "送3000积分"
			}
		]
	}
]