/**
 * 验证当前解析方法的正确性
 */

console.log('=== 验证当前解析方法 ===\n');

// 从最新日志中提取的信息
const LATEST_XOR_ARRAY = [2049, 543, 1778, 167, 4020];
const LATEST_TOKEN = '3w8y7e';
const EXPECTED_TOKEN = '7jdcpm'; // 抓包中的实际token

console.log(`最新XOR数组: [${LATEST_XOR_ARRAY.join(', ')}]`);
console.log(`最新生成token: ${LATEST_TOKEN}`);
console.log(`期望token: ${EXPECTED_TOKEN}`);
console.log(`匹配: ${LATEST_TOKEN === EXPECTED_TOKEN ? '✅' : '❌'}\n`);

// 分析为什么不匹配
function analyzeTokenDifference() {
    console.log('1. 分析token差异:');
    
    const chars = 'abcdefghijklmnopqrstuvwxyz0123456789';
    
    console.log('字符对比:');
    for (let i = 0; i < Math.max(LATEST_TOKEN.length, EXPECTED_TOKEN.length); i++) {
        const latestChar = LATEST_TOKEN[i] || '?';
        const expectedChar = EXPECTED_TOKEN[i] || '?';
        const latestIndex = chars.indexOf(latestChar);
        const expectedIndex = chars.indexOf(expectedChar);
        
        console.log(`位置${i}: '${latestChar}'(${latestIndex}) vs '${expectedChar}'(${expectedIndex}) ${latestChar === expectedChar ? '✅' : '❌'}`);
    }
    
    console.log('\nASCII对比:');
    for (let i = 0; i < Math.max(LATEST_TOKEN.length, EXPECTED_TOKEN.length); i++) {
        const latestChar = LATEST_TOKEN[i] || '?';
        const expectedChar = EXPECTED_TOKEN[i] || '?';
        const latestASCII = latestChar.charCodeAt(0);
        const expectedASCII = expectedChar.charCodeAt(0);
        
        console.log(`位置${i}: ${latestASCII} vs ${expectedASCII} (差值: ${latestASCII - expectedASCII})`);
    }
}

// 分析XOR数组的差异
function analyzeXORDifference() {
    console.log('\n2. 分析XOR数组差异:');
    
    const OLD_XOR_ARRAY = [2212, 53, 2852, 3445, 3327]; // 抓包中的
    const NEW_XOR_ARRAY = [2049, 543, 1778, 167, 4020]; // 最新的
    
    console.log(`抓包XOR数组: [${OLD_XOR_ARRAY.join(', ')}]`);
    console.log(`最新XOR数组: [${NEW_XOR_ARRAY.join(', ')}]`);
    
    console.log('\n数组对比:');
    for (let i = 0; i < 5; i++) {
        const oldVal = OLD_XOR_ARRAY[i];
        const newVal = NEW_XOR_ARRAY[i];
        const diff = newVal - oldVal;
        console.log(`位置${i}: ${oldVal} -> ${newVal} (差值: ${diff})`);
    }
    
    const oldSum = OLD_XOR_ARRAY.reduce((sum, val) => sum + val, 0);
    const newSum = NEW_XOR_ARRAY.reduce((sum, val) => sum + val, 0);
    console.log(`\n数组和: ${oldSum} -> ${newSum} (差值: ${newSum - oldSum})`);
}

// 测试不同的token生成方法
function testDifferentMethods() {
    console.log('\n3. 测试不同的token生成方法:');
    
    const chars = 'abcdefghijklmnopqrstuvwxyz0123456789';
    const xorArray = LATEST_XOR_ARRAY;
    
    // 方法1: 直接映射
    let token1 = '';
    for (let i = 0; i < 6; i++) {
        const index = xorArray[i % xorArray.length] % chars.length;
        token1 += chars[index];
    }
    console.log(`方法1 (直接映射): ${token1} ${token1 === EXPECTED_TOKEN ? '✅' : '❌'}`);
    
    // 方法2: 基于数组和
    const sum = xorArray.reduce((s, v) => s + v, 0);
    let token2 = '';
    for (let i = 0; i < 6; i++) {
        const index = (sum + xorArray[i % xorArray.length] + i * 13) % chars.length;
        token2 += chars[index];
    }
    console.log(`方法2 (数组和): ${token2} ${token2 === EXPECTED_TOKEN ? '✅' : '❌'}`);
    
    // 方法3: 基于哈希
    let hash = 0;
    xorArray.forEach((val, idx) => {
        hash += val * (idx + 1);
    });
    let token3 = '';
    for (let i = 0; i < 6; i++) {
        const index = (hash + i * 7) % chars.length;
        token3 += chars[index];
    }
    console.log(`方法3 (哈希): ${token3} ${token3 === EXPECTED_TOKEN ? '✅' : '❌'}`);
    
    // 方法4: 尝试逆向工程期望token
    console.log('\n尝试逆向工程期望token:');
    const expectedASCII = EXPECTED_TOKEN.split('').map(c => c.charCodeAt(0));
    console.log(`期望ASCII: [${expectedASCII.join(', ')}]`);
    
    // 检查是否能通过某种XOR运算得到期望的ASCII值
    expectedASCII.forEach((ascii, index) => {
        console.log(`\n字符'${EXPECTED_TOKEN[index]}' (ASCII ${ascii}):`);
        
        // 尝试找到输入值
        xorArray.forEach((xorVal, xorIndex) => {
            const input = ascii ^ xorVal;
            console.log(`  XOR[${xorIndex}](${input}) ^ ${xorVal} = ${input ^ xorVal} ${input ^ xorVal === ascii ? '✅' : '❌'}`);
        });
    });
}

// 检查VM解析的window属性模式
function analyzeWindowPattern() {
    console.log('\n4. 分析window属性模式:');
    
    // 从日志中提取的一些window属性
    const windowProps = [
        '4bf02d65', 'sur3qj9e',
        '989d8fet', 'vzdvb7s6',
        'b9e8bf14', 'kfvebz',
        'ae3c0620', 'vwo2uz910',
        // ... 更多属性
        '3c9df71d', '3w8y7e' // 最后一个
    ];
    
    console.log('Window属性模式:');
    console.log('- 属性名: 8位十六进制字符串');
    console.log('- 属性值: 6-9位字母数字组合');
    console.log(`- 最后一个属性值: ${windowProps[windowProps.length - 1]}`);
    console.log(`- 是否为期望token: ${windowProps[windowProps.length - 1] === EXPECTED_TOKEN ? '✅' : '❌'}`);
    
    // 分析属性名的模式
    console.log('\n属性名分析:');
    windowProps.forEach((prop, index) => {
        if (index % 2 === 0) { // 属性名
            console.log(`${prop}: 长度${prop.length}, 十六进制: ${/^[0-9a-f]+$/.test(prop) ? '✅' : '❌'}`);
        }
    });
}

// 主要结论
function drawConclusions() {
    console.log('\n=== 结论 ===');
    
    console.log('1. 当前解析方法的问题:');
    console.log('   - VM成功解析了混淆代码');
    console.log('   - 获取到了window属性中的token');
    console.log('   - 但生成的token与期望不符');
    
    console.log('\n2. 可能的原因:');
    console.log('   - 抓包数据与当前请求的混淆代码不同');
    console.log('   - token生成算法随时间变化');
    console.log('   - 我们选择了错误的window属性');
    console.log('   - 服务器端有额外的验证逻辑');
    
    console.log('\n3. 建议的解决方案:');
    console.log('   - 获取更多抓包数据进行对比');
    console.log('   - 分析window属性的选择逻辑');
    console.log('   - 测试不同的token候选');
    console.log('   - 检查时间窗口和其他验证条件');
    
    if (LATEST_TOKEN !== EXPECTED_TOKEN) {
        console.log('\n❌ 当前方法无法复现抓包token');
        console.log('需要进一步分析混淆代码的真正逻辑');
    } else {
        console.log('\n✅ 当前方法成功复现抓包token');
    }
}

// 运行所有分析
function main() {
    analyzeTokenDifference();
    analyzeXORDifference();
    testDifferentMethods();
    analyzeWindowPattern();
    drawConclusions();
}

main();
