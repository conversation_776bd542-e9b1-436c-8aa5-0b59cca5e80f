/**
 * Token生成测试脚本
 * 用于验证我们的token解析逻辑
 */

// 模拟抓包数据中的混淆JavaScript代码片段
const mockTokenScript = `
var/*qNVOMRa*/var/*RyR687XEI83CJb8fHR*/__qxBY2hC/*YMXkbo*/=\\u0053\\u0074\\u0072\\u0069\\u006e\\u0067
/*lXX1kbkRXJ9FmZqK*/./*MZCeD*/\\u0066r\\u006fm\\u0043ha\\u0072C\\u006fde/*sMmtyNpcAyql8O*/;
var/*Cfhx2UMXDK6BMZM*/_x_sEd = [/*dF4tvpacOzsnV9*/2212,53,2852,3445,3327,/*SPaJmEBdtdDh0xIQ5b*/];//vYnpXz6vIEdS5RW
var/*bcNoFOVIxVdidN*/_$3vge/*TLSE8sLsN11d*/=/*WloJg5inrhui0kzNK*/function(/*IzKb6MJKvAxCI*/){\n/*4XMcxf8q6w5Eu9E8*/return/*tjsXXWm*/arguments[/*7KLqBRABhc6cioDsfg*/0]^/*NkiyNuHr*/\n/*PX3neKSxLULiZ3uE*/_x_sEd[/*oZo7hG3hKugxQh6c5Sh*/0];/*Gy6Av9AnY5HTsFbp*/}/*QeINbNufqrnfY*/;
`;

// 简单哈希函数
function simpleHash(str) {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
        const char = str.charCodeAt(i);
        hash = ((hash << 5) - hash) + char;
        hash = hash & hash; // 转换为32位整数
    }
    return Math.abs(hash);
}

// 专门的token解析器
function parseObfuscatedToken(tokenScript, timestamp) {
    try {
        console.log(`开始解析混淆token脚本...`);
        
        // 方法1: 尝试提取关键数组和函数
        const arrayMatch = tokenScript.match(/_x_sEd\s*=\s*\[([^\]]+)\]/);
        let xorArray = [2212, 53, 2852, 3445, 3327]; // 从抓包数据中提取的默认值
        
        if (arrayMatch && arrayMatch[1]) {
            const arrayStr = arrayMatch[1].replace(/\/\*[^*]*\*\//g, '').replace(/\s+/g, '');
            const numbers = arrayStr.split(',').map(s => {
                const trimmed = s.trim();
                const num = parseInt(trimmed);
                return isNaN(num) ? parseInt(trimmed, 16) || 0 : num;
            });
            if (numbers.length >= 5) {
                xorArray = numbers.slice(0, 5);
                console.log(`提取到XOR数组:`, xorArray);
            }
        }

        // 方法2: 基于时间戳生成token
        const timeStr = timestamp.toString();
        const timeHash = simpleHash(timeStr);
        
        // 生成6位token
        const chars = 'abcdefghijklmnopqrstuvwxyz0123456789';
        let token = '';
        
        // 使用时间戳和XOR数组生成token
        for (let i = 0; i < 6; i++) {
            const xorValue = xorArray[i % xorArray.length];
            const timeValue = parseInt(timeStr.charAt(i % timeStr.length)) || 0;
            const hashValue = (timeHash + i) % chars.length;
            const index = (xorValue ^ timeValue ^ hashValue) % chars.length;
            token += chars[index];
        }
        
        console.log(`基于时间戳生成token: ${token}`);
        return token;
        
    } catch (error) {
        console.log(`token解析失败: ${error.message}`);
        return null;
    }
}

// 备用token生成器
function generateFallbackToken(timestamp) {
    const chars = 'abcdefghijklmnopqrstuvwxyz0123456789';
    const timeStr = timestamp.toString();
    let token = '';
    
    // 基于时间戳生成6位token
    for (let i = 0; i < 6; i++) {
        const timeValue = parseInt(timeStr.charAt(i % timeStr.length)) || 0;
        const index = (timeValue * (i + 1) + timestamp % 1000) % chars.length;
        token += chars[index];
    }
    
    return token;
}

// 测试函数
function testTokenGeneration() {
    console.log('=== Token生成测试 ===\n');
    
    // 使用抓包数据中的时间戳
    const testTimestamp = 1754383264519;
    console.log(`测试时间戳: ${testTimestamp}`);
    console.log(`时间戳对应时间: ${new Date(testTimestamp).toLocaleString()}\n`);
    
    // 测试专门解析器
    console.log('1. 测试专门解析器:');
    const token1 = parseObfuscatedToken(mockTokenScript, testTimestamp);
    console.log(`生成的token: ${token1}\n`);
    
    // 测试备用生成器
    console.log('2. 测试备用生成器:');
    const token2 = generateFallbackToken(testTimestamp);
    console.log(`生成的token: ${token2}\n`);
    
    // 测试当前时间戳
    const currentTimestamp = Date.now();
    console.log('3. 测试当前时间戳:');
    console.log(`当前时间戳: ${currentTimestamp}`);
    const token3 = parseObfuscatedToken(mockTokenScript, currentTimestamp);
    console.log(`生成的token: ${token3}\n`);
    
    // 多次测试看规律
    console.log('4. 多次测试看规律:');
    for (let i = 0; i < 5; i++) {
        const ts = Date.now() + i * 1000;
        const token = parseObfuscatedToken(mockTokenScript, ts);
        console.log(`时间戳: ${ts} -> token: ${token}`);
    }
}

// 运行测试
testTokenGeneration();
